// NewWave Payment Gateway Client-Side JavaScript

class PaymentClient {
  constructor() {
    this.apiBaseUrl = window.location.origin + '/api';
    this.jwtSecret = 'test_jwt_secret_key_123'; // In production, this should be configured properly
    this.currentToken = null;
  }

  // JWT Token Generation (client-side)
  async generateJWT(payload) {
    const jwtConfig = {
      issuer: 'newwave-payment-gateway',
      subject: 'payment-client',
      audience: 'newwave-api',
      accessTokenExpiry: 900 // 15 minutes
    };

    const basePayload = {
      iss: jwtConfig.issuer,
      sub: jwtConfig.subject,
      aud: jwtConfig.audience,
      iat: Math.floor(Date.now() / 1000),
      jti: this.generateUUID(),
      request: payload,
      timestamp: Date.now(),
      type: 'access',
      exp: Math.floor(Date.now() / 1000) + jwtConfig.accessTokenExpiry
    };

    return await this.signJWT(basePayload, this.jwtSecret);
  }

  // Simple JWT signing for client-side (Note: In production, use proper JWT library)
  async signJWT(payload, secret) {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    };

    const encodedHeader = this.base64UrlEncode(JSON.stringify(header));
    const encodedPayload = this.base64UrlEncode(JSON.stringify(payload));
    const data = `${encodedHeader}.${encodedPayload}`;
    
    const signature = await this.hmacSHA256(data, secret);
    const encodedSignature = this.base64UrlEncode(signature);
    
    return `${data}.${encodedSignature}`;
  }

  // HMAC SHA256 implementation
  async hmacSHA256(message, key) {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key);
    const messageData = encoder.encode(message);

    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    return new Uint8Array(signature);
  }

  // Base64 URL encoding
  base64UrlEncode(data) {
    if (typeof data === 'string') {
      data = new TextEncoder().encode(data);
    }
    
    const base64 = btoa(String.fromCharCode(...data));
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }

  // Generate UUID
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // API Request helper
  async makeRequest(endpoint, method = 'GET', data = null, requiresAuth = true) {
    const url = `${this.apiBaseUrl}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json'
    };

    if (requiresAuth && this.currentToken) {
      headers['Authorization'] = `Bearer ${this.currentToken}`;
    }

    const config = {
      method,
      headers
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }
      
      return result;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Initiate Payment
  async initiatePayment(paymentData) {
    try {
      // Generate JWT token for this request
      this.currentToken = await this.generateJWT(paymentData);
      
      const result = await this.makeRequest('/payment/initiate', 'POST', paymentData);
      return result;
    } catch (error) {
      console.error('Payment initiation failed:', error);
      throw error;
    }
  }

  // Process Mobile Money Payment
  async processMobileMoneyPayment(mobileMoneyData) {
    try {
      if (!this.currentToken) {
        throw new Error('No authentication token available');
      }
      
      const result = await this.makeRequest('/payment/mobile-money', 'POST', mobileMoneyData);
      return result;
    } catch (error) {
      console.error('Mobile money payment failed:', error);
      throw error;
    }
  }

  // Process Card Payment
  async processCardPayment(cardData) {
    try {
      if (!this.currentToken) {
        throw new Error('No authentication token available');
      }
      
      const result = await this.makeRequest('/payment/card', 'POST', cardData);
      return result;
    } catch (error) {
      console.error('Card payment failed:', error);
      throw error;
    }
  }

  // Check Payment Status
  async checkPaymentStatus(transactionToken) {
    try {
      if (!this.currentToken) {
        throw new Error('No authentication token available');
      }
      
      const result = await this.makeRequest(`/payment/status/${transactionToken}`, 'GET');
      return result;
    } catch (error) {
      console.error('Status check failed:', error);
      throw error;
    }
  }

  // Form Validation Helpers
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  validateMalawiPhone(phone) {
    const phoneRegex = /^\+265[0-9]{9}$/;
    return phoneRegex.test(phone);
  }

  validateAmount(amount) {
    return !isNaN(amount) && parseFloat(amount) > 0;
  }

  validateCardNumber(cardNumber) {
    // Basic Luhn algorithm check
    const cleaned = cardNumber.replace(/\s/g, '');
    if (!/^\d{13,19}$/.test(cleaned)) return false;
    
    let sum = 0;
    let isEven = false;
    
    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) digit -= 9;
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  }

  validateCVV(cvv) {
    return /^\d{3,4}$/.test(cvv);
  }

  validateExpiry(expiry) {
    const expiryRegex = /^(0[1-9]|1[0-2])\/\d{2}$/;
    if (!expiryRegex.test(expiry)) return false;
    
    const [month, year] = expiry.split('/');
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;
    
    const expiryYear = parseInt(year);
    const expiryMonth = parseInt(month);
    
    if (expiryYear < currentYear) return false;
    if (expiryYear === currentYear && expiryMonth < currentMonth) return false;
    
    return true;
  }

  // UI Helper Functions
  showLoading(buttonElement, text = 'Processing...') {
    if (buttonElement) {
      buttonElement.disabled = true;
      buttonElement.innerHTML = `<span class="loading"></span>${text}`;
    }
  }

  hideLoading(buttonElement, originalText) {
    if (buttonElement) {
      buttonElement.disabled = false;
      buttonElement.innerHTML = originalText;
    }
  }

  showAlert(message, type = 'info', containerId = 'alert-container') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    container.innerHTML = '';
    container.appendChild(alert);
    
    // Auto-hide after 5 seconds for non-error messages
    if (type !== 'error') {
      setTimeout(() => {
        if (alert.parentNode) {
          alert.parentNode.removeChild(alert);
        }
      }, 5000);
    }
  }

  // Format currency
  formatCurrency(amount, currency = 'MWK') {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  }

  // Format phone number
  formatPhoneNumber(phone) {
    if (!phone.startsWith('+265')) {
      // Remove leading zeros and add country code
      const cleaned = phone.replace(/^0+/, '');
      return `+265${cleaned}`;
    }
    return phone;
  }

  // Generate company reference
  generateCompanyRef() {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `NW-${dateStr}-${randomNum}`;
  }
}

// Initialize global payment client
window.paymentClient = new PaymentClient();

// Utility functions for form handling
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return false;
  
  let isValid = true;
  const inputs = form.querySelectorAll('input[required], select[required]');
  
  inputs.forEach(input => {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    // Remove existing error state
    formGroup.classList.remove('error');
    if (errorElement) {
      errorElement.remove();
    }
    
    // Validate input
    let inputValid = true;
    let errorMessage = '';
    
    if (!input.value.trim()) {
      inputValid = false;
      errorMessage = 'This field is required';
    } else {
      // Specific validations
      switch (input.type) {
        case 'email':
          if (!window.paymentClient.validateEmail(input.value)) {
            inputValid = false;
            errorMessage = 'Please enter a valid email address';
          }
          break;
        case 'tel':
          if (input.name === 'phone' && !window.paymentClient.validateMalawiPhone(input.value)) {
            inputValid = false;
            errorMessage = 'Please enter a valid Malawi phone number (+265XXXXXXXXX)';
          }
          break;
        case 'number':
          if (input.name === 'paymentAmount' && !window.paymentClient.validateAmount(input.value)) {
            inputValid = false;
            errorMessage = 'Please enter a valid amount';
          }
          break;
      }
    }
    
    if (!inputValid) {
      isValid = false;
      formGroup.classList.add('error');
      
      const errorDiv = document.createElement('div');
      errorDiv.className = 'error-message';
      errorDiv.textContent = errorMessage;
      formGroup.appendChild(errorDiv);
    }
  });
  
  return isValid;
}

// Auto-format phone numbers
document.addEventListener('DOMContentLoaded', function() {
  const phoneInputs = document.querySelectorAll('input[name="phone"], input[name="phoneNumber"]');
  phoneInputs.forEach(input => {
    input.addEventListener('blur', function() {
      if (this.value) {
        this.value = window.paymentClient.formatPhoneNumber(this.value);
      }
    });
  });
});
