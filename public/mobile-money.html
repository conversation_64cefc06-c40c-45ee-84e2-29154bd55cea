<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Money Payment - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Complete your payment with Mobile Money</p>
        </div>

        <div id="alert-container"></div>

        <div class="card">
            <h2>📱 Mobile Money Payment</h2>
            <p>Pay securely using your Airtel Money or TNM Mpamba account</p>

            <form id="mobile-money-form">
                <div class="form-group">
                    <label for="transactionToken">Transaction Token *</label>
                    <input type="text" id="transactionToken" name="transactionToken" required readonly 
                           style="background-color: var(--gray-100); font-family: monospace;">
                    <small style="color: var(--gray-600); font-size: 14px;">This token was generated when you initiated the payment</small>
                </div>

                <div class="form-group">
                    <label for="phoneNumber">Mobile Money Phone Number *</label>
                    <input type="tel" id="phoneNumber" name="phoneNumber" required 
                           placeholder="+************" pattern="^\+265[0-9]{9}$">
                    <small style="color: var(--gray-600); font-size: 14px;">Format: +265XXXXXXXXX (must match your mobile money account)</small>
                </div>

                <div class="form-group">
                    <label for="mno">Mobile Network Operator *</label>
                    <select id="mno" name="mno" required>
                        <option value="">Select your mobile network</option>
                        <option value="AirtelMW">Airtel Money (Malawi)</option>
                        <option value="TNMMW">TNM Mpamba (Malawi)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="mnoCountry">Country *</label>
                    <select id="mnoCountry" name="mnoCountry" required>
                        <option value="MW">MW - Malawi</option>
                    </select>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        Process Mobile Money Payment
                    </button>
                    <a href="/payment-form.html" class="btn btn-secondary">Back to Payment Form</a>
                    <a href="/index.html" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>

        <!-- Payment Instructions -->
        <div class="card">
            <h3>📋 Payment Instructions</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 15px;">🔵 Airtel Money</h4>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li>Ensure you have sufficient balance in your Airtel Money account</li>
                        <li>Enter your Airtel Money phone number above</li>
                        <li>Click "Process Mobile Money Payment"</li>
                        <li>You will receive an SMS prompt on your phone</li>
                        <li>Follow the SMS instructions to authorize the payment</li>
                        <li>Enter your Airtel Money PIN when prompted</li>
                    </ol>
                </div>
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 15px;">🟡 TNM Mpamba</h4>
                    <ol style="padding-left: 20px; line-height: 1.8;">
                        <li>Ensure you have sufficient balance in your Mpamba account</li>
                        <li>Enter your TNM Mpamba phone number above</li>
                        <li>Click "Process Mobile Money Payment"</li>
                        <li>You will receive an SMS prompt on your phone</li>
                        <li>Follow the SMS instructions to authorize the payment</li>
                        <li>Enter your Mpamba PIN when prompted</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="card" style="background: var(--gray-50); border-left: 4px solid var(--warning-color);">
            <h3>🔒 Security Notice</h3>
            <ul style="padding-left: 20px; line-height: 1.8;">
                <li><strong>Never share your mobile money PIN</strong> with anyone</li>
                <li><strong>Only authorize payments</strong> that you initiated</li>
                <li><strong>Check the amount carefully</strong> before confirming the payment</li>
                <li><strong>Keep your transaction token safe</strong> for future reference</li>
                <li><strong>Contact support immediately</strong> if you notice any suspicious activity</li>
            </ul>
        </div>

        <!-- Payment Status -->
        <div id="payment-status" class="card hidden">
            <h3>Payment Status</h3>
            <div id="status-content">
                <!-- Status will be populated here -->
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-primary" id="check-status-btn">
                    Check Status Again
                </button>
                <a href="/payment-status.html" class="btn btn-secondary" id="status-page-btn">
                    Go to Status Page
                </a>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get transaction token from URL or session storage
            const urlParams = new URLSearchParams(window.location.search);
            const tokenFromUrl = urlParams.get('token');
            const tokenFromSession = sessionStorage.getItem('transactionToken');
            
            const transactionToken = tokenFromUrl || tokenFromSession;
            
            if (transactionToken) {
                document.getElementById('transactionToken').value = transactionToken;
            } else {
                window.paymentClient.showAlert('No transaction token found. Please initiate a payment first.', 'warning');
                setTimeout(() => {
                    window.location.href = '/payment-form.html';
                }, 3000);
                return;
            }

            // Handle form submission
            document.getElementById('mobile-money-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                if (!validateForm('mobile-money-form')) {
                    return;
                }

                const submitBtn = document.getElementById('submit-btn');
                const originalText = submitBtn.innerHTML;
                
                try {
                    window.paymentClient.showLoading(submitBtn, 'Processing Payment...');
                    
                    // Collect form data
                    const formData = new FormData(this);
                    const mobileMoneyData = {
                        transactionToken: formData.get('transactionToken'),
                        phoneNumber: formData.get('phoneNumber'),
                        mno: formData.get('mno'),
                        mnoCountry: formData.get('mnoCountry')
                    };

                    // Process mobile money payment
                    const result = await window.paymentClient.processMobileMoneyPayment(mobileMoneyData);
                    
                    window.paymentClient.showAlert('Mobile money payment request sent! Please check your phone for SMS instructions.', 'success');
                    
                    // Show payment status section
                    showPaymentStatus();
                    
                    // Start polling for payment status
                    startStatusPolling(mobileMoneyData.transactionToken);
                    
                } catch (error) {
                    console.error('Mobile money payment failed:', error);
                    window.paymentClient.showAlert(`Mobile money payment failed: ${error.message}`, 'error');
                } finally {
                    window.paymentClient.hideLoading(submitBtn, originalText);
                }
            });

            function showPaymentStatus() {
                document.getElementById('payment-status').classList.remove('hidden');
                document.getElementById('payment-status').scrollIntoView({ behavior: 'smooth' });
                
                // Update status page link
                const statusPageBtn = document.getElementById('status-page-btn');
                statusPageBtn.href = `/payment-status.html?token=${document.getElementById('transactionToken').value}`;
            }

            function startStatusPolling(transactionToken) {
                let pollCount = 0;
                const maxPolls = 30; // Poll for 5 minutes (30 * 10 seconds)
                
                const pollInterval = setInterval(async () => {
                    pollCount++;
                    
                    try {
                        const status = await window.paymentClient.checkPaymentStatus(transactionToken);
                        updateStatusDisplay(status);
                        
                        // Stop polling if payment is completed or failed
                        if (status.transactionStatus === 'APPROVED' || 
                            status.transactionStatus === 'DECLINED' || 
                            status.transactionStatus === 'CANCELLED' ||
                            pollCount >= maxPolls) {
                            clearInterval(pollInterval);
                        }
                        
                    } catch (error) {
                        console.error('Status check failed:', error);
                        if (pollCount >= maxPolls) {
                            clearInterval(pollInterval);
                            updateStatusDisplay({
                                transactionStatus: 'TIMEOUT',
                                message: 'Status check timed out. Please check manually.'
                            });
                        }
                    }
                }, 10000); // Poll every 10 seconds
            }

            function updateStatusDisplay(status) {
                const statusContent = document.getElementById('status-content');
                const statusClass = getStatusClass(status.transactionStatus);
                
                statusContent.innerHTML = `
                    <div class="transaction-details">
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">
                                <span class="status-indicator ${statusClass}">${status.transactionStatus || 'PENDING'}</span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Message:</span>
                            <span class="detail-value">${status.message || 'Processing...'}</span>
                        </div>
                        ${status.resultExplanation ? `
                        <div class="detail-row">
                            <span class="detail-label">Details:</span>
                            <span class="detail-value">${status.resultExplanation}</span>
                        </div>
                        ` : ''}
                        <div class="detail-row">
                            <span class="detail-label">Last Updated:</span>
                            <span class="detail-value">${new Date().toLocaleString()}</span>
                        </div>
                    </div>
                `;
                
                // Show appropriate message based on status
                if (status.transactionStatus === 'APPROVED') {
                    window.paymentClient.showAlert('Payment completed successfully!', 'success');
                } else if (status.transactionStatus === 'DECLINED') {
                    window.paymentClient.showAlert('Payment was declined. Please try again or contact support.', 'error');
                } else if (status.transactionStatus === 'CANCELLED') {
                    window.paymentClient.showAlert('Payment was cancelled.', 'warning');
                }
            }

            function getStatusClass(status) {
                switch (status) {
                    case 'APPROVED': return 'status-approved';
                    case 'DECLINED': return 'status-declined';
                    case 'CANCELLED': return 'status-cancelled';
                    default: return 'status-pending';
                }
            }

            // Handle manual status check
            document.getElementById('check-status-btn').addEventListener('click', async function() {
                const token = document.getElementById('transactionToken').value;
                if (!token) return;
                
                const btn = this;
                const originalText = btn.innerHTML;
                
                try {
                    window.paymentClient.showLoading(btn, 'Checking...');
                    const status = await window.paymentClient.checkPaymentStatus(token);
                    updateStatusDisplay(status);
                } catch (error) {
                    window.paymentClient.showAlert(`Status check failed: ${error.message}`, 'error');
                } finally {
                    window.paymentClient.hideLoading(btn, originalText);
                }
            });
        });
    </script>
</body>
</html>
