<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <style>
        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }
        
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .checkmark {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--success-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            margin: 0 auto 30px;
            animation: checkmarkAppear 0.8s ease-out;
        }
        
        @keyframes checkmarkAppear {
            0% { 
                transform: scale(0) rotate(180deg);
                opacity: 0;
            }
            100% { 
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }
        
        .confetti {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }
        
        .confetti-piece {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--primary-color);
            animation: confettiFall 3s linear infinite;
        }
        
        @keyframes confettiFall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="confetti" id="confetti"></div>
    
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Payment completed successfully!</p>
        </div>

        <div id="alert-container"></div>

        <div class="card success-animation" style="text-align: center; background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 2px solid var(--success-color);">
            <div class="checkmark">✓</div>
            <h2 style="color: var(--success-color); margin-bottom: 20px;">Payment Successful!</h2>
            <p style="font-size: 18px; margin-bottom: 30px; color: var(--gray-700);">
                Your payment has been processed successfully. Thank you for using NewWave Payment Gateway!
            </p>
            
            <div id="transaction-details" class="transaction-details" style="text-align: left; margin: 30px 0;">
                <!-- Transaction details will be populated here -->
            </div>
            
            <div class="btn-group" style="justify-content: center;">
                <button type="button" class="btn btn-primary" id="download-receipt-btn">
                    📄 Download Receipt
                </button>
                <button type="button" class="btn btn-secondary" id="email-receipt-btn">
                    📧 Email Receipt
                </button>
                <a href="/payment-form.html" class="btn btn-secondary">New Payment</a>
                <a href="/index.html" class="btn btn-secondary">Back to Home</a>
            </div>
        </div>

        <div class="card">
            <h3>🎉 What's Next?</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center; padding: 20px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 15px;">📧</div>
                    <h4>Confirmation Email</h4>
                    <p style="font-size: 14px; color: var(--gray-600); line-height: 1.6;">
                        A confirmation email with your receipt and transaction details has been sent to your registered email address.
                    </p>
                </div>
                <div style="text-align: center; padding: 20px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 15px;">📱</div>
                    <h4>SMS Notification</h4>
                    <p style="font-size: 14px; color: var(--gray-600); line-height: 1.6;">
                        You will receive an SMS confirmation on your registered mobile number within a few minutes.
                    </p>
                </div>
                <div style="text-align: center; padding: 20px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 15px;">🔍</div>
                    <h4>Track Status</h4>
                    <p style="font-size: 14px; color: var(--gray-600); line-height: 1.6;">
                        You can always check your payment status using the transaction token provided below.
                    </p>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📞 Need Support?</h3>
            <p>If you have any questions about your payment or need assistance, our support team is here to help.</p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center;">
                    <h4 style="color: var(--primary-color);">📧 Email Support</h4>
                    <p><a href="mailto:<EMAIL>" style="color: var(--primary-color);"><EMAIL></a></p>
                    <small style="color: var(--gray-600);">Response within 24 hours</small>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: var(--primary-color);">📞 Phone Support</h4>
                    <p style="font-weight: 600;">+265 1 234 567</p>
                    <small style="color: var(--gray-600);">Available 24/7</small>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: var(--primary-color);">💬 Live Chat</h4>
                    <p><a href="#" style="color: var(--primary-color);">Start Chat</a></p>
                    <small style="color: var(--gray-600);">Mon-Fri 8AM-6PM</small>
                </div>
            </div>
        </div>

        <div class="card" style="background: var(--gray-50); text-align: center;">
            <h3>⭐ Rate Your Experience</h3>
            <p>How was your payment experience with NewWave?</p>
            <div style="margin: 20px 0;">
                <button class="btn btn-secondary" onclick="rateExperience(1)">⭐</button>
                <button class="btn btn-secondary" onclick="rateExperience(2)">⭐⭐</button>
                <button class="btn btn-secondary" onclick="rateExperience(3)">⭐⭐⭐</button>
                <button class="btn btn-secondary" onclick="rateExperience(4)">⭐⭐⭐⭐</button>
                <button class="btn btn-secondary" onclick="rateExperience(5)">⭐⭐⭐⭐⭐</button>
            </div>
            <div id="rating-feedback" style="margin-top: 15px;"></div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create confetti effect
            createConfetti();
            
            // Get transaction details from URL parameters or session storage
            const urlParams = new URLSearchParams(window.location.search);
            const transactionToken = urlParams.get('token') || sessionStorage.getItem('transactionToken');
            const paymentData = sessionStorage.getItem('paymentData');
            
            if (transactionToken) {
                displayTransactionDetails(transactionToken, paymentData);
                // Check final status to confirm success
                checkFinalStatus(transactionToken);
            } else {
                // Show generic success message
                displayGenericSuccess();
            }
            
            // Handle receipt download
            document.getElementById('download-receipt-btn').addEventListener('click', function() {
                downloadReceipt();
            });
            
            // Handle email receipt
            document.getElementById('email-receipt-btn').addEventListener('click', function() {
                emailReceipt();
            });
        });

        function createConfetti() {
            const confettiContainer = document.getElementById('confetti');
            const colors = ['#2563eb', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
            
            for (let i = 0; i < 50; i++) {
                const confettiPiece = document.createElement('div');
                confettiPiece.className = 'confetti-piece';
                confettiPiece.style.left = Math.random() * 100 + '%';
                confettiPiece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confettiPiece.style.animationDelay = Math.random() * 3 + 's';
                confettiPiece.style.animationDuration = (Math.random() * 2 + 2) + 's';
                confettiContainer.appendChild(confettiPiece);
            }
            
            // Remove confetti after animation
            setTimeout(() => {
                confettiContainer.innerHTML = '';
            }, 5000);
        }

        function displayTransactionDetails(transactionToken, paymentDataStr) {
            const detailsContainer = document.getElementById('transaction-details');
            let paymentData = null;
            
            try {
                paymentData = paymentDataStr ? JSON.parse(paymentDataStr) : null;
            } catch (error) {
                console.error('Failed to parse payment data:', error);
            }
            
            let detailsHtml = `
                <h4 style="margin-bottom: 15px; color: var(--gray-800);">Transaction Details</h4>
                <div class="detail-row">
                    <span class="detail-label">Transaction Token:</span>
                    <span class="detail-value" style="font-family: monospace; word-break: break-all;">${transactionToken}</span>
                </div>
            `;
            
            if (paymentData && paymentData.transaction) {
                const transaction = paymentData.transaction;
                detailsHtml += `
                    <div class="detail-row">
                        <span class="detail-label">Amount:</span>
                        <span class="detail-value">${window.paymentClient.formatCurrency(transaction.paymentAmount, transaction.paymentCurrency)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Currency:</span>
                        <span class="detail-value">${transaction.paymentCurrency}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Reference:</span>
                        <span class="detail-value">${transaction.companyRef}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Customer:</span>
                        <span class="detail-value">${transaction.customer.firstName} ${transaction.customer.lastName}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value">${transaction.customer.email}</span>
                    </div>
                `;
                
                if (paymentData.services && paymentData.services.length > 0) {
                    const service = paymentData.services[0];
                    detailsHtml += `
                        <div class="detail-row">
                            <span class="detail-label">Service:</span>
                            <span class="detail-value">${service.serviceDescription}</span>
                        </div>
                    `;
                }
            }
            
            detailsHtml += `
                <div class="detail-row">
                    <span class="detail-label">Completed:</span>
                    <span class="detail-value">${new Date().toLocaleString()}</span>
                </div>
            `;
            
            detailsContainer.innerHTML = detailsHtml;
        }

        function displayGenericSuccess() {
            const detailsContainer = document.getElementById('transaction-details');
            detailsContainer.innerHTML = `
                <h4 style="margin-bottom: 15px; color: var(--gray-800);">Transaction Details</h4>
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value"><span class="status-indicator status-approved">✅ APPROVED</span></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Completed:</span>
                    <span class="detail-value">${new Date().toLocaleString()}</span>
                </div>
            `;
        }

        async function checkFinalStatus(transactionToken) {
            try {
                const status = await window.paymentClient.checkPaymentStatus(transactionToken);
                if (status.transactionStatus !== 'APPROVED') {
                    // If not approved, redirect to appropriate page
                    if (status.transactionStatus === 'DECLINED') {
                        window.location.href = `/cancel.html?token=${transactionToken}&reason=declined`;
                    } else if (status.transactionStatus === 'CANCELLED') {
                        window.location.href = `/cancel.html?token=${transactionToken}&reason=cancelled`;
                    }
                }
            } catch (error) {
                console.error('Failed to verify final status:', error);
                // Continue showing success page even if status check fails
            }
        }

        function downloadReceipt() {
            const transactionToken = new URLSearchParams(window.location.search).get('token') || 
                                   sessionStorage.getItem('transactionToken');
            
            if (!transactionToken) {
                window.paymentClient.showAlert('No transaction data available for receipt', 'warning');
                return;
            }
            
            // Create a simple receipt content
            const receiptContent = document.getElementById('transaction-details').innerHTML;
            const receiptHtml = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Payment Receipt - NewWave</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .detail-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #eee; }
                        .detail-label { font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🌊 NewWave Payment Gateway</h1>
                        <h2>Payment Receipt</h2>
                    </div>
                    ${receiptContent}
                    <p style="margin-top: 30px; text-align: center; color: #666;">
                        Thank you for using NewWave Payment Gateway
                    </p>
                </body>
                </html>
            `;
            
            // Create and download the receipt
            const blob = new Blob([receiptHtml], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `newwave-receipt-${transactionToken.substring(0, 8)}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            window.paymentClient.showAlert('Receipt downloaded successfully!', 'success');
        }

        function emailReceipt() {
            const paymentDataStr = sessionStorage.getItem('paymentData');
            let customerEmail = '';
            
            try {
                const paymentData = paymentDataStr ? JSON.parse(paymentDataStr) : null;
                if (paymentData && paymentData.transaction && paymentData.transaction.customer) {
                    customerEmail = paymentData.transaction.customer.email;
                }
            } catch (error) {
                console.error('Failed to get customer email:', error);
            }
            
            if (!customerEmail) {
                customerEmail = prompt('Please enter your email address to receive the receipt:');
                if (!customerEmail) return;
            }
            
            // In a real implementation, this would send an API request to email the receipt
            window.paymentClient.showAlert(`Receipt will be sent to ${customerEmail}`, 'success');
            
            // Simulate email sending
            setTimeout(() => {
                window.paymentClient.showAlert('Receipt sent successfully!', 'success');
            }, 2000);
        }

        function rateExperience(rating) {
            const feedbackDiv = document.getElementById('rating-feedback');
            const messages = {
                1: 'We\'re sorry to hear that. Please contact support to help us improve.',
                2: 'Thank you for your feedback. We\'ll work to improve your experience.',
                3: 'Thank you for your feedback. We appreciate your input.',
                4: 'Great! Thank you for the positive feedback.',
                5: 'Excellent! Thank you for the 5-star rating!'
            };
            
            feedbackDiv.innerHTML = `
                <div class="alert alert-success">
                    ${messages[rating]} Your rating: ${'⭐'.repeat(rating)}
                </div>
            `;
            
            // In a real implementation, this would send the rating to your analytics system
            console.log(`User rated experience: ${rating} stars`);
        }
    </script>
</body>
</html>
