<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initiate Payment - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Initiate a new payment transaction</p>
        </div>

        <div id="alert-container"></div>

        <div class="card">
            <h2>Payment Details</h2>
            <form id="payment-form">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <!-- Transaction Details -->
                    <div>
                        <h3 style="margin-bottom: 20px; color: var(--primary-color);">Transaction Information</h3>
                        
                        <div class="form-group">
                            <label for="paymentAmount">Payment Amount (MWK) *</label>
                            <input type="number" id="paymentAmount" name="paymentAmount" required min="1" step="0.01" placeholder="e.g., 15000.00">
                        </div>

                        <div class="form-group">
                            <label for="paymentCurrency">Currency *</label>
                            <select id="paymentCurrency" name="paymentCurrency" required>
                                <option value="MWK">MWK - Malawi Kwacha</option>
                                <option value="USD">USD - US Dollar</option>
                                <option value="EUR">EUR - Euro</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="companyRef">Company Reference</label>
                            <input type="text" id="companyRef" name="companyRef" placeholder="Auto-generated if empty">
                        </div>

                        <div class="form-group">
                            <label for="redirectURL">Success Redirect URL</label>
                            <input type="url" id="redirectURL" name="redirectURL" placeholder="https://your-site.com/success" value="https://dev.newwave.com/payment/success">
                        </div>

                        <div class="form-group">
                            <label for="backURL">Cancel Redirect URL</label>
                            <input type="url" id="backURL" name="backURL" placeholder="https://your-site.com/cancel" value="https://dev.newwave.com/payment/cancel">
                        </div>
                    </div>

                    <!-- Customer Details -->
                    <div>
                        <h3 style="margin-bottom: 20px; color: var(--primary-color);">Customer Information</h3>
                        
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required placeholder="e.g., John">
                        </div>

                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required placeholder="e.g., Banda">
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required placeholder="e.g., <EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="phone">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required placeholder="+265994567890" pattern="^\+265[0-9]{9}$">
                            <small style="color: var(--gray-600); font-size: 14px;">Format: +265XXXXXXXXX</small>
                        </div>
                    </div>
                </div>

                <!-- Service Details -->
                <div style="margin-top: 30px;">
                    <h3 style="margin-bottom: 20px; color: var(--primary-color);">Service Information</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="serviceType">Service Type *</label>
                            <select id="serviceType" name="serviceType" required>
                                <option value="5525">5525 - Internet Data Bundle</option>
                                <option value="5526">5526 - Voice Bundle</option>
                                <option value="5527">5527 - SMS Bundle</option>
                                <option value="5528">5528 - Mixed Bundle</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="serviceDescription">Service Description *</label>
                            <input type="text" id="serviceDescription" name="serviceDescription" required placeholder="e.g., Internet Data Bundle">
                        </div>

                        <div class="form-group">
                            <label for="serviceDate">Service Date *</label>
                            <input type="date" id="serviceDate" name="serviceDate" required>
                        </div>

                        <div class="form-group">
                            <label for="serviceFrom">Service From (Optional)</label>
                            <input type="text" id="serviceFrom" name="serviceFrom" placeholder="e.g., LIL">
                        </div>

                        <div class="form-group">
                            <label for="serviceTo">Service To (Optional)</label>
                            <input type="text" id="serviceTo" name="serviceTo" placeholder="e.g., LIL">
                        </div>
                    </div>
                </div>

                <div class="btn-group" style="margin-top: 30px;">
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        Initiate Payment
                    </button>
                    <a href="/index.html" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>

        <!-- Payment Summary (Hidden initially) -->
        <div id="payment-summary" class="card hidden">
            <h3>Payment Summary</h3>
            <div class="transaction-details">
                <div class="detail-row">
                    <span class="detail-label">Amount:</span>
                    <span class="detail-value" id="summary-amount"></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Currency:</span>
                    <span class="detail-value" id="summary-currency"></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Customer:</span>
                    <span class="detail-value" id="summary-customer"></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Service:</span>
                    <span class="detail-value" id="summary-service"></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Transaction Token:</span>
                    <span class="detail-value" id="summary-token" style="font-family: monospace; word-break: break-all;"></span>
                </div>
            </div>
            
            <div class="btn-group">
                <a href="/mobile-money.html" class="btn btn-primary" id="mobile-money-btn">
                    📱 Pay with Mobile Money
                </a>
                <a href="/card-payment.html" class="btn btn-primary" id="card-payment-btn">
                    💳 Pay with Card
                </a>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set default service date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('serviceDate').value = today;

            // Auto-generate company reference if empty
            document.getElementById('companyRef').placeholder = window.paymentClient.generateCompanyRef();

            // Update service description based on service type
            document.getElementById('serviceType').addEventListener('change', function() {
                const descriptions = {
                    '5525': 'Internet Data Bundle',
                    '5526': 'Voice Bundle',
                    '5527': 'SMS Bundle',
                    '5528': 'Mixed Bundle'
                };
                document.getElementById('serviceDescription').value = descriptions[this.value] || '';
            });

            // Handle form submission
            document.getElementById('payment-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                if (!validateForm('payment-form')) {
                    return;
                }

                const submitBtn = document.getElementById('submit-btn');
                const originalText = submitBtn.innerHTML;
                
                try {
                    window.paymentClient.showLoading(submitBtn, 'Initiating Payment...');
                    
                    // Collect form data
                    const formData = new FormData(this);
                    const paymentData = {
                        transaction: {
                            paymentAmount: parseFloat(formData.get('paymentAmount')),
                            paymentCurrency: formData.get('paymentCurrency'),
                            companyRef: formData.get('companyRef') || window.paymentClient.generateCompanyRef(),
                            redirectURL: formData.get('redirectURL') || window.location.origin + '/success.html',
                            backURL: formData.get('backURL') || window.location.origin + '/cancel.html',
                            customer: {
                                firstName: formData.get('firstName'),
                                lastName: formData.get('lastName'),
                                email: formData.get('email'),
                                phone: formData.get('phone')
                            }
                        },
                        services: [{
                            serviceType: formData.get('serviceType'),
                            serviceDescription: formData.get('serviceDescription'),
                            serviceDate: formData.get('serviceDate'),
                            serviceFrom: formData.get('serviceFrom') || 'LIL',
                            serviceTo: formData.get('serviceTo') || 'LIL'
                        }]
                    };

                    // Initiate payment
                    const result = await window.paymentClient.initiatePayment(paymentData);
                    
                    // Store transaction token for next steps
                    sessionStorage.setItem('transactionToken', result.transactionToken);
                    sessionStorage.setItem('paymentData', JSON.stringify(paymentData));
                    
                    // Show payment summary
                    showPaymentSummary(paymentData, result.transactionToken);
                    
                    window.paymentClient.showAlert('Payment initiated successfully! Choose your payment method below.', 'success');
                    
                } catch (error) {
                    console.error('Payment initiation failed:', error);
                    window.paymentClient.showAlert(`Payment initiation failed: ${error.message}`, 'error');
                } finally {
                    window.paymentClient.hideLoading(submitBtn, originalText);
                }
            });

            function showPaymentSummary(paymentData, transactionToken) {
                // Populate summary
                document.getElementById('summary-amount').textContent = 
                    window.paymentClient.formatCurrency(paymentData.transaction.paymentAmount, paymentData.transaction.paymentCurrency);
                document.getElementById('summary-currency').textContent = paymentData.transaction.paymentCurrency;
                document.getElementById('summary-customer').textContent = 
                    `${paymentData.transaction.customer.firstName} ${paymentData.transaction.customer.lastName}`;
                document.getElementById('summary-service').textContent = paymentData.services[0].serviceDescription;
                document.getElementById('summary-token').textContent = transactionToken;
                
                // Update payment method links with transaction token
                const mobileMoneyBtn = document.getElementById('mobile-money-btn');
                const cardPaymentBtn = document.getElementById('card-payment-btn');
                
                mobileMoneyBtn.href = `/mobile-money.html?token=${transactionToken}`;
                cardPaymentBtn.href = `/card-payment.html?token=${transactionToken}`;
                
                // Show summary and hide form
                document.getElementById('payment-summary').classList.remove('hidden');
                document.getElementById('payment-form').style.display = 'none';
                
                // Scroll to summary
                document.getElementById('payment-summary').scrollIntoView({ behavior: 'smooth' });
            }
        });
    </script>
</body>
</html>
