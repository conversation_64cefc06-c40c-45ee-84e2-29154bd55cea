<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Payment - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Complete your payment with Credit/Debit Card</p>
        </div>

        <div id="alert-container"></div>

        <div class="card">
            <h2>💳 Credit/Debit Card Payment</h2>
            <p>Pay securely using your Visa, Mastercard, or other supported credit/debit cards</p>

            <form id="card-payment-form">
                <div class="form-group">
                    <label for="transactionToken">Transaction Token *</label>
                    <input type="text" id="transactionToken" name="transactionToken" required readonly 
                           style="background-color: var(--gray-100); font-family: monospace;">
                    <small style="color: var(--gray-600); font-size: 14px;">This token was generated when you initiated the payment</small>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 20px; color: var(--primary-color);">Card Information</h3>
                        
                        <div class="form-group">
                            <label for="cardHolderName">Cardholder Name *</label>
                            <input type="text" id="cardHolderName" name="cardHolderName" required 
                                   placeholder="e.g., John Banda" style="text-transform: uppercase;">
                            <small style="color: var(--gray-600); font-size: 14px;">Enter name exactly as it appears on your card</small>
                        </div>

                        <div class="form-group">
                            <label for="creditCardNumber">Card Number *</label>
                            <input type="text" id="creditCardNumber" name="creditCardNumber" required 
                                   placeholder="1234 5678 9012 3456" maxlength="19" autocomplete="cc-number">
                            <small style="color: var(--gray-600); font-size: 14px;">16-digit card number (spaces will be added automatically)</small>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div class="form-group">
                                <label for="creditCardExpiry">Expiry Date *</label>
                                <input type="text" id="creditCardExpiry" name="creditCardExpiry" required 
                                       placeholder="MM/YY" maxlength="5" autocomplete="cc-exp">
                                <small style="color: var(--gray-600); font-size: 14px;">MM/YY format</small>
                            </div>

                            <div class="form-group">
                                <label for="creditCardCVV">CVV *</label>
                                <input type="text" id="creditCardCVV" name="creditCardCVV" required 
                                       placeholder="123" maxlength="4" autocomplete="cc-csc">
                                <small style="color: var(--gray-600); font-size: 14px;">3-4 digit security code</small>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 style="margin-bottom: 20px; color: var(--primary-color);">Security Features</h3>
                        
                        <div style="background: var(--gray-50); padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 15px;">🔒 Your payment is protected by:</h4>
                            <ul style="padding-left: 20px; line-height: 1.8;">
                                <li>256-bit SSL encryption</li>
                                <li>PCI DSS compliance</li>
                                <li>3D Secure authentication</li>
                                <li>Fraud detection systems</li>
                            </ul>
                        </div>

                        <div style="background: var(--gray-50); padding: 20px; border-radius: 8px;">
                            <h4 style="margin-bottom: 15px;">💳 Accepted Cards:</h4>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <span style="padding: 5px 10px; background: white; border-radius: 4px; font-size: 14px; font-weight: 600;">VISA</span>
                                <span style="padding: 5px 10px; background: white; border-radius: 4px; font-size: 14px; font-weight: 600;">Mastercard</span>
                                <span style="padding: 5px 10px; background: white; border-radius: 4px; font-size: 14px; font-weight: 600;">American Express</span>
                                <span style="padding: 5px 10px; background: white; border-radius: 4px; font-size: 14px; font-weight: 600;">Discover</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="btn-group" style="margin-top: 30px;">
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        🔒 Process Card Payment
                    </button>
                    <a href="/payment-form.html" class="btn btn-secondary">Back to Payment Form</a>
                    <a href="/index.html" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="card" style="background: var(--gray-50); border-left: 4px solid var(--success-color);">
            <h3>🛡️ Security & Privacy</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">Data Protection</h4>
                    <ul style="padding-left: 20px; line-height: 1.6; font-size: 14px;">
                        <li>Card details are encrypted in transit</li>
                        <li>No card information is stored on our servers</li>
                        <li>PCI DSS Level 1 compliant processing</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: var(--primary-color); margin-bottom: 10px;">Fraud Prevention</h4>
                    <ul style="padding-left: 20px; line-height: 1.6; font-size: 14px;">
                        <li>Real-time fraud monitoring</li>
                        <li>3D Secure authentication when required</li>
                        <li>Advanced risk assessment algorithms</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Payment Status -->
        <div id="payment-status" class="card hidden">
            <h3>Payment Status</h3>
            <div id="status-content">
                <!-- Status will be populated here -->
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-primary" id="check-status-btn">
                    Check Status Again
                </button>
                <a href="/payment-status.html" class="btn btn-secondary" id="status-page-btn">
                    Go to Status Page
                </a>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get transaction token from URL or session storage
            const urlParams = new URLSearchParams(window.location.search);
            const tokenFromUrl = urlParams.get('token');
            const tokenFromSession = sessionStorage.getItem('transactionToken');
            
            const transactionToken = tokenFromUrl || tokenFromSession;
            
            if (transactionToken) {
                document.getElementById('transactionToken').value = transactionToken;
            } else {
                window.paymentClient.showAlert('No transaction token found. Please initiate a payment first.', 'warning');
                setTimeout(() => {
                    window.location.href = '/payment-form.html';
                }, 3000);
                return;
            }

            // Format card number input
            document.getElementById('creditCardNumber').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
                let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                if (formattedValue !== e.target.value) {
                    e.target.value = formattedValue;
                }
            });

            // Format expiry date input
            document.getElementById('creditCardExpiry').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4);
                }
                e.target.value = value;
            });

            // CVV input - numbers only
            document.getElementById('creditCardCVV').addEventListener('input', function(e) {
                e.target.value = e.target.value.replace(/[^0-9]/g, '');
            });

            // Cardholder name - uppercase
            document.getElementById('cardHolderName').addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase();
            });

            // Custom validation for card form
            function validateCardForm() {
                let isValid = true;
                const form = document.getElementById('card-payment-form');
                
                // Basic required field validation
                if (!validateForm('card-payment-form')) {
                    isValid = false;
                }
                
                // Card number validation
                const cardNumber = document.getElementById('creditCardNumber').value.replace(/\s/g, '');
                if (!window.paymentClient.validateCardNumber(cardNumber)) {
                    showFieldError('creditCardNumber', 'Please enter a valid card number');
                    isValid = false;
                }
                
                // CVV validation
                const cvv = document.getElementById('creditCardCVV').value;
                if (!window.paymentClient.validateCVV(cvv)) {
                    showFieldError('creditCardCVV', 'Please enter a valid CVV (3-4 digits)');
                    isValid = false;
                }
                
                // Expiry validation
                const expiry = document.getElementById('creditCardExpiry').value;
                if (!window.paymentClient.validateExpiry(expiry)) {
                    showFieldError('creditCardExpiry', 'Please enter a valid expiry date (MM/YY)');
                    isValid = false;
                }
                
                return isValid;
            }

            function showFieldError(fieldId, message) {
                const field = document.getElementById(fieldId);
                const formGroup = field.closest('.form-group');
                
                // Remove existing error
                formGroup.classList.remove('error');
                const existingError = formGroup.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error
                formGroup.classList.add('error');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = message;
                formGroup.appendChild(errorDiv);
            }

            // Handle form submission
            document.getElementById('card-payment-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                if (!validateCardForm()) {
                    return;
                }

                const submitBtn = document.getElementById('submit-btn');
                const originalText = submitBtn.innerHTML;
                
                try {
                    window.paymentClient.showLoading(submitBtn, '🔒 Processing Payment...');
                    
                    // Collect form data
                    const formData = new FormData(this);
                    const cardData = {
                        transactionToken: formData.get('transactionToken'),
                        cardHolderName: formData.get('cardHolderName'),
                        creditCardNumber: formData.get('creditCardNumber').replace(/\s/g, ''),
                        creditCardExpiry: formData.get('creditCardExpiry'),
                        creditCardCVV: formData.get('creditCardCVV')
                    };

                    // Process card payment
                    const result = await window.paymentClient.processCardPayment(cardData);
                    
                    window.paymentClient.showAlert('Card payment processed successfully! Please wait for confirmation.', 'success');
                    
                    // Clear sensitive form data
                    document.getElementById('creditCardNumber').value = '';
                    document.getElementById('creditCardCVV').value = '';
                    
                    // Show payment status section
                    showPaymentStatus();
                    
                    // Start polling for payment status
                    startStatusPolling(cardData.transactionToken);
                    
                } catch (error) {
                    console.error('Card payment failed:', error);
                    window.paymentClient.showAlert(`Card payment failed: ${error.message}`, 'error');
                } finally {
                    window.paymentClient.hideLoading(submitBtn, originalText);
                }
            });

            function showPaymentStatus() {
                document.getElementById('payment-status').classList.remove('hidden');
                document.getElementById('payment-status').scrollIntoView({ behavior: 'smooth' });
                
                // Update status page link
                const statusPageBtn = document.getElementById('status-page-btn');
                statusPageBtn.href = `/payment-status.html?token=${document.getElementById('transactionToken').value}`;
            }

            function startStatusPolling(transactionToken) {
                let pollCount = 0;
                const maxPolls = 30; // Poll for 5 minutes
                
                const pollInterval = setInterval(async () => {
                    pollCount++;
                    
                    try {
                        const status = await window.paymentClient.checkPaymentStatus(transactionToken);
                        updateStatusDisplay(status);
                        
                        // Stop polling if payment is completed or failed
                        if (status.transactionStatus === 'APPROVED' || 
                            status.transactionStatus === 'DECLINED' || 
                            status.transactionStatus === 'CANCELLED' ||
                            pollCount >= maxPolls) {
                            clearInterval(pollInterval);
                        }
                        
                    } catch (error) {
                        console.error('Status check failed:', error);
                        if (pollCount >= maxPolls) {
                            clearInterval(pollInterval);
                            updateStatusDisplay({
                                transactionStatus: 'TIMEOUT',
                                message: 'Status check timed out. Please check manually.'
                            });
                        }
                    }
                }, 10000); // Poll every 10 seconds
            }

            function updateStatusDisplay(status) {
                const statusContent = document.getElementById('status-content');
                const statusClass = getStatusClass(status.transactionStatus);
                
                statusContent.innerHTML = `
                    <div class="transaction-details">
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">
                                <span class="status-indicator ${statusClass}">${status.transactionStatus || 'PENDING'}</span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Message:</span>
                            <span class="detail-value">${status.message || 'Processing...'}</span>
                        </div>
                        ${status.resultExplanation ? `
                        <div class="detail-row">
                            <span class="detail-label">Details:</span>
                            <span class="detail-value">${status.resultExplanation}</span>
                        </div>
                        ` : ''}
                        <div class="detail-row">
                            <span class="detail-label">Last Updated:</span>
                            <span class="detail-value">${new Date().toLocaleString()}</span>
                        </div>
                    </div>
                `;
                
                // Show appropriate message based on status
                if (status.transactionStatus === 'APPROVED') {
                    window.paymentClient.showAlert('Payment completed successfully!', 'success');
                } else if (status.transactionStatus === 'DECLINED') {
                    window.paymentClient.showAlert('Payment was declined. Please check your card details and try again.', 'error');
                } else if (status.transactionStatus === 'CANCELLED') {
                    window.paymentClient.showAlert('Payment was cancelled.', 'warning');
                }
            }

            function getStatusClass(status) {
                switch (status) {
                    case 'APPROVED': return 'status-approved';
                    case 'DECLINED': return 'status-declined';
                    case 'CANCELLED': return 'status-cancelled';
                    default: return 'status-pending';
                }
            }

            // Handle manual status check
            document.getElementById('check-status-btn').addEventListener('click', async function() {
                const token = document.getElementById('transactionToken').value;
                if (!token) return;
                
                const btn = this;
                const originalText = btn.innerHTML;
                
                try {
                    window.paymentClient.showLoading(btn, 'Checking...');
                    const status = await window.paymentClient.checkPaymentStatus(token);
                    updateStatusDisplay(status);
                } catch (error) {
                    window.paymentClient.showAlert(`Status check failed: ${error.message}`, 'error');
                } finally {
                    window.paymentClient.hideLoading(btn, originalText);
                }
            });
        });
    </script>
</body>
</html>
