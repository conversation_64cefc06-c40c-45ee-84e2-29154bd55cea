<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Cancelled - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <style>
        .cancel-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--warning-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            margin: 0 auto 30px;
            animation: cancelShake 0.8s ease-out;
        }
        
        @keyframes cancelShake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .declined-icon {
            background: var(--error-color);
        }
        
        .timeout-icon {
            background: var(--gray-600);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p id="header-subtitle">Payment was cancelled</p>
        </div>

        <div id="alert-container"></div>

        <div class="card" style="text-align: center; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border: 2px solid var(--warning-color);" id="main-card">
            <div class="cancel-icon" id="status-icon">✕</div>
            <h2 style="color: var(--warning-color); margin-bottom: 20px;" id="main-title">Payment Cancelled</h2>
            <p style="font-size: 18px; margin-bottom: 30px; color: var(--gray-700);" id="main-message">
                Your payment was cancelled. No charges have been made to your account.
            </p>
            
            <div id="transaction-details" class="transaction-details" style="text-align: left; margin: 30px 0;">
                <!-- Transaction details will be populated here -->
            </div>
            
            <div class="btn-group" style="justify-content: center;">
                <a href="/payment-form.html" class="btn btn-primary">Try Again</a>
                <a href="/payment-status.html" class="btn btn-secondary" id="check-status-btn">Check Status</a>
                <a href="/index.html" class="btn btn-secondary">Back to Home</a>
            </div>
        </div>

        <div class="card" id="reason-card">
            <h3>Why was my payment cancelled?</h3>
            <div id="reason-content">
                <!-- Reason content will be populated based on the cancellation type -->
            </div>
        </div>

        <div class="card">
            <h3>🔄 What can you do next?</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center; padding: 20px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 15px;">🔄</div>
                    <h4>Try Again</h4>
                    <p style="font-size: 14px; color: var(--gray-600); line-height: 1.6;">
                        Start a new payment with the same or different payment method. Make sure you have sufficient funds.
                    </p>
                    <a href="/payment-form.html" class="btn btn-primary" style="margin-top: 10px;">New Payment</a>
                </div>
                <div style="text-align: center; padding: 20px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 15px;">💳</div>
                    <h4>Different Method</h4>
                    <p style="font-size: 14px; color: var(--gray-600); line-height: 1.6;">
                        Try using a different payment method. Switch between mobile money and credit card options.
                    </p>
                    <div style="margin-top: 10px;">
                        <a href="/mobile-money.html" class="btn btn-secondary" style="margin: 5px;">Mobile Money</a>
                        <a href="/card-payment.html" class="btn btn-secondary" style="margin: 5px;">Credit Card</a>
                    </div>
                </div>
                <div style="text-align: center; padding: 20px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 15px;">📞</div>
                    <h4>Get Help</h4>
                    <p style="font-size: 14px; color: var(--gray-600); line-height: 1.6;">
                        Contact our support team if you continue to experience issues with your payments.
                    </p>
                    <a href="mailto:<EMAIL>" class="btn btn-secondary" style="margin-top: 10px;">Contact Support</a>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>🛡️ Security Notice</h3>
            <div style="background: var(--gray-50); padding: 20px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <p style="margin-bottom: 15px;"><strong>Your account is safe:</strong></p>
                <ul style="padding-left: 20px; line-height: 1.8;">
                    <li>No charges have been made to your account</li>
                    <li>Your payment information remains secure</li>
                    <li>You can safely try the payment again</li>
                    <li>All cancelled transactions are logged for security</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>📞 Need Support?</h3>
            <p>If you're experiencing repeated payment issues or need assistance, our support team is ready to help.</p>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center;">
                    <h4 style="color: var(--primary-color);">📧 Email Support</h4>
                    <p><a href="mailto:<EMAIL>" style="color: var(--primary-color);"><EMAIL></a></p>
                    <small style="color: var(--gray-600);">Response within 24 hours</small>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: var(--primary-color);">📞 Phone Support</h4>
                    <p style="font-weight: 600;">+265 1 234 567</p>
                    <small style="color: var(--gray-600);">Available 24/7</small>
                </div>
                <div style="text-align: center;">
                    <h4 style="color: var(--primary-color);">💬 Live Chat</h4>
                    <p><a href="#" style="color: var(--primary-color);">Start Chat</a></p>
                    <small style="color: var(--gray-600);">Mon-Fri 8AM-6PM</small>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get cancellation details from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const transactionToken = urlParams.get('token');
            const reason = urlParams.get('reason');
            const message = urlParams.get('message');
            
            // Update page based on cancellation reason
            updatePageForReason(reason, message);
            
            // Display transaction details if available
            if (transactionToken) {
                displayTransactionDetails(transactionToken);
                document.getElementById('check-status-btn').href = `/payment-status.html?token=${transactionToken}`;
            } else {
                // Hide check status button if no token
                document.getElementById('check-status-btn').style.display = 'none';
            }
            
            // Check if there's payment data in session storage
            const paymentData = sessionStorage.getItem('paymentData');
            if (paymentData) {
                displayPaymentData(paymentData);
            }
        });

        function updatePageForReason(reason, message) {
            const headerSubtitle = document.getElementById('header-subtitle');
            const mainCard = document.getElementById('main-card');
            const statusIcon = document.getElementById('status-icon');
            const mainTitle = document.getElementById('main-title');
            const mainMessage = document.getElementById('main-message');
            const reasonCard = document.getElementById('reason-card');
            const reasonContent = document.getElementById('reason-content');
            
            switch (reason) {
                case 'declined':
                    headerSubtitle.textContent = 'Payment was declined';
                    mainCard.style.background = 'linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%)';
                    mainCard.style.borderColor = 'var(--error-color)';
                    statusIcon.className = 'cancel-icon declined-icon';
                    statusIcon.textContent = '✕';
                    mainTitle.textContent = 'Payment Declined';
                    mainTitle.style.color = 'var(--error-color)';
                    mainMessage.textContent = 'Your payment was declined by the payment provider. Please check your payment details and try again.';
                    
                    reasonContent.innerHTML = `
                        <p>Your payment was declined for one of the following reasons:</p>
                        <ul style="padding-left: 20px; line-height: 1.8;">
                            <li><strong>Insufficient funds:</strong> Your account doesn't have enough balance</li>
                            <li><strong>Invalid card details:</strong> Check your card number, expiry date, or CVV</li>
                            <li><strong>Expired card:</strong> Your card has expired</li>
                            <li><strong>Card blocked:</strong> Your bank has blocked the transaction</li>
                            <li><strong>Daily limit exceeded:</strong> You've reached your daily transaction limit</li>
                            <li><strong>Security restrictions:</strong> The transaction was flagged for security reasons</li>
                        </ul>
                        <div style="margin-top: 20px; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                            <strong>What to do:</strong> Contact your bank or mobile money provider to resolve the issue, then try again.
                        </div>
                    `;
                    break;
                    
                case 'timeout':
                    headerSubtitle.textContent = 'Payment timed out';
                    mainCard.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #d1d5db 100%)';
                    mainCard.style.borderColor = 'var(--gray-600)';
                    statusIcon.className = 'cancel-icon timeout-icon';
                    statusIcon.textContent = '⏰';
                    mainTitle.textContent = 'Payment Timed Out';
                    mainTitle.style.color = 'var(--gray-600)';
                    mainMessage.textContent = 'Your payment session has expired. Please start a new payment.';
                    
                    reasonContent.innerHTML = `
                        <p>Your payment timed out because:</p>
                        <ul style="padding-left: 20px; line-height: 1.8;">
                            <li><strong>Session expired:</strong> Payment sessions expire after 15 minutes of inactivity</li>
                            <li><strong>No response:</strong> You didn't complete the payment process in time</li>
                            <li><strong>Network issues:</strong> Connection problems prevented completion</li>
                        </ul>
                        <div style="margin-top: 20px; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                            <strong>What to do:</strong> Start a new payment. Make sure you have a stable internet connection and complete the process promptly.
                        </div>
                    `;
                    break;
                    
                case 'user':
                default:
                    // Default cancellation (user cancelled)
                    reasonContent.innerHTML = `
                        <p>You cancelled the payment process. This could happen when:</p>
                        <ul style="padding-left: 20px; line-height: 1.8;">
                            <li><strong>Manual cancellation:</strong> You clicked the cancel button</li>
                            <li><strong>Browser navigation:</strong> You navigated away from the payment page</li>
                            <li><strong>Mobile money:</strong> You declined the SMS prompt on your phone</li>
                            <li><strong>Card payment:</strong> You cancelled during 3D Secure authentication</li>
                        </ul>
                        <div style="margin-top: 20px; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                            <strong>What to do:</strong> If you want to complete the payment, simply start a new transaction.
                        </div>
                    `;
                    break;
            }
            
            // Show custom message if provided
            if (message) {
                mainMessage.textContent = decodeURIComponent(message);
            }
        }

        function displayTransactionDetails(transactionToken) {
            const detailsContainer = document.getElementById('transaction-details');
            
            let detailsHtml = `
                <h4 style="margin-bottom: 15px; color: var(--gray-800);">Transaction Details</h4>
                <div class="detail-row">
                    <span class="detail-label">Transaction Token:</span>
                    <span class="detail-value" style="font-family: monospace; word-break: break-all;">${transactionToken}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value"><span class="status-indicator status-cancelled">🚫 CANCELLED</span></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Cancelled:</span>
                    <span class="detail-value">${new Date().toLocaleString()}</span>
                </div>
            `;
            
            detailsContainer.innerHTML = detailsHtml;
        }

        function displayPaymentData(paymentDataStr) {
            try {
                const paymentData = JSON.parse(paymentDataStr);
                const detailsContainer = document.getElementById('transaction-details');
                
                if (paymentData && paymentData.transaction) {
                    const transaction = paymentData.transaction;
                    const currentHtml = detailsContainer.innerHTML;
                    
                    const additionalDetails = `
                        <div class="detail-row">
                            <span class="detail-label">Amount:</span>
                            <span class="detail-value">${window.paymentClient.formatCurrency(transaction.paymentAmount, transaction.paymentCurrency)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Reference:</span>
                            <span class="detail-value">${transaction.companyRef}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Customer:</span>
                            <span class="detail-value">${transaction.customer.firstName} ${transaction.customer.lastName}</span>
                        </div>
                    `;
                    
                    // Insert additional details before the last detail row
                    const lastDetailIndex = currentHtml.lastIndexOf('<div class="detail-row">');
                    if (lastDetailIndex !== -1) {
                        const newHtml = currentHtml.substring(0, lastDetailIndex) + 
                                       additionalDetails + 
                                       currentHtml.substring(lastDetailIndex);
                        detailsContainer.innerHTML = newHtml;
                    }
                }
            } catch (error) {
                console.error('Failed to parse payment data:', error);
            }
        }
    </script>
</body>
</html>
