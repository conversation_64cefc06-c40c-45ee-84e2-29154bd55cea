<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Status - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Check your payment transaction status</p>
        </div>

        <div id="alert-container"></div>

        <div class="card">
            <h2>📊 Payment Status Checker</h2>
            <p>Enter your transaction token to check the current status of your payment</p>

            <form id="status-form">
                <div class="form-group">
                    <label for="transactionToken">Transaction Token *</label>
                    <input type="text" id="transactionToken" name="transactionToken" required 
                           placeholder="e.g., D0B6F0EA-9EAE-4399-B370-2CA0E49AED49"
                           style="font-family: monospace;">
                    <small style="color: var(--gray-600); font-size: 14px;">
                        This token was provided when you initiated the payment
                    </small>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary" id="check-btn">
                        Check Payment Status
                    </button>
                    <button type="button" class="btn btn-secondary" id="auto-refresh-btn">
                        Enable Auto-Refresh
                    </button>
                    <a href="/payment-form.html" class="btn btn-secondary">New Payment</a>
                </div>
            </form>
        </div>

        <!-- Status Display -->
        <div id="status-display" class="card hidden">
            <h3>Payment Status</h3>
            <div id="status-content">
                <!-- Status content will be populated here -->
            </div>
            
            <div class="btn-group">
                <button type="button" class="btn btn-primary" id="refresh-btn">
                    🔄 Refresh Status
                </button>
                <button type="button" class="btn btn-secondary" id="copy-token-btn">
                    📋 Copy Token
                </button>
                <a href="/index.html" class="btn btn-secondary">Back to Home</a>
            </div>
        </div>

        <!-- Status History -->
        <div id="status-history" class="card hidden">
            <h3>Status History</h3>
            <div id="history-content">
                <!-- History will be populated here -->
            </div>
        </div>

        <!-- Help Section -->
        <div class="card">
            <h3>❓ Understanding Payment Status</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: var(--success-color);">✅ APPROVED</h4>
                    <p style="font-size: 14px; line-height: 1.6;">
                        Payment has been successfully processed and completed. 
                        Funds have been transferred and the transaction is final.
                    </p>
                </div>
                <div>
                    <h4 style="color: var(--warning-color);">⏳ PENDING</h4>
                    <p style="font-size: 14px; line-height: 1.6;">
                        Payment is being processed. For mobile money, check your phone for SMS prompts. 
                        For cards, 3D Secure authentication may be required.
                    </p>
                </div>
                <div>
                    <h4 style="color: var(--error-color);">❌ DECLINED</h4>
                    <p style="font-size: 14px; line-height: 1.6;">
                        Payment was declined by the payment provider. This could be due to 
                        insufficient funds, invalid card details, or security restrictions.
                    </p>
                </div>
                <div>
                    <h4 style="color: var(--gray-600);">🚫 CANCELLED</h4>
                    <p style="font-size: 14px; line-height: 1.6;">
                        Payment was cancelled by the user or timed out. 
                        You can initiate a new payment if needed.
                    </p>
                </div>
            </div>
        </div>

        <!-- Troubleshooting -->
        <div class="card" style="background: var(--gray-50);">
            <h3>🔧 Troubleshooting</h3>
            <div style="margin-top: 20px;">
                <h4 style="margin-bottom: 15px;">If your payment is stuck in PENDING status:</h4>
                <ul style="padding-left: 20px; line-height: 1.8;">
                    <li><strong>Mobile Money:</strong> Check your phone for SMS prompts and follow the instructions</li>
                    <li><strong>Credit Card:</strong> Complete any 3D Secure authentication if prompted</li>
                    <li><strong>Network Issues:</strong> Ensure you have a stable internet connection</li>
                    <li><strong>Timeout:</strong> Payments may timeout after 15 minutes of inactivity</li>
                </ul>
                
                <h4 style="margin: 20px 0 15px;">If your payment was DECLINED:</h4>
                <ul style="padding-left: 20px; line-height: 1.8;">
                    <li>Check that you have sufficient funds in your account</li>
                    <li>Verify that your card details are correct</li>
                    <li>Ensure your card is not expired or blocked</li>
                    <li>Contact your bank if the issue persists</li>
                </ul>
                
                <div style="margin-top: 20px; padding: 15px; background: white; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                    <strong>Need Help?</strong> Contact our support team at 
                    <a href="mailto:<EMAIL>" style="color: var(--primary-color);"><EMAIL></a> 
                    or call +265 1 234 567
                </div>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        let autoRefreshInterval = null;
        let statusHistory = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Get transaction token from URL if provided
            const urlParams = new URLSearchParams(window.location.search);
            const tokenFromUrl = urlParams.get('token');
            
            if (tokenFromUrl) {
                document.getElementById('transactionToken').value = tokenFromUrl;
                // Auto-check status if token is provided in URL
                checkPaymentStatus();
            }

            // Handle status form submission
            document.getElementById('status-form').addEventListener('submit', function(e) {
                e.preventDefault();
                checkPaymentStatus();
            });

            // Handle refresh button
            document.getElementById('refresh-btn').addEventListener('click', function() {
                checkPaymentStatus();
            });

            // Handle auto-refresh toggle
            document.getElementById('auto-refresh-btn').addEventListener('click', function() {
                toggleAutoRefresh();
            });

            // Handle copy token button
            document.getElementById('copy-token-btn').addEventListener('click', function() {
                copyTokenToClipboard();
            });

            async function checkPaymentStatus() {
                const token = document.getElementById('transactionToken').value.trim();
                if (!token) {
                    window.paymentClient.showAlert('Please enter a transaction token', 'warning');
                    return;
                }

                const checkBtn = document.getElementById('check-btn');
                const refreshBtn = document.getElementById('refresh-btn');
                const activeBtn = checkBtn.style.display !== 'none' ? checkBtn : refreshBtn;
                const originalText = activeBtn.innerHTML;

                try {
                    window.paymentClient.showLoading(activeBtn, 'Checking...');
                    
                    const status = await window.paymentClient.checkPaymentStatus(token);
                    
                    // Add to history
                    addToHistory(status);
                    
                    // Display status
                    displayStatus(status);
                    
                    // Show status display section
                    document.getElementById('status-display').classList.remove('hidden');
                    document.getElementById('status-history').classList.remove('hidden');
                    
                    // Scroll to status
                    document.getElementById('status-display').scrollIntoView({ behavior: 'smooth' });
                    
                } catch (error) {
                    console.error('Status check failed:', error);
                    window.paymentClient.showAlert(`Status check failed: ${error.message}`, 'error');
                } finally {
                    window.paymentClient.hideLoading(activeBtn, originalText);
                }
            }

            function displayStatus(status) {
                const statusContent = document.getElementById('status-content');
                const statusClass = getStatusClass(status.transactionStatus);
                const statusIcon = getStatusIcon(status.transactionStatus);
                
                statusContent.innerHTML = `
                    <div class="transaction-details">
                        <div class="detail-row">
                            <span class="detail-label">Transaction Token:</span>
                            <span class="detail-value" style="font-family: monospace; word-break: break-all;">
                                ${status.transToken || 'N/A'}
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">
                                <span class="status-indicator ${statusClass}">
                                    ${statusIcon} ${status.transactionStatus || 'UNKNOWN'}
                                </span>
                            </span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Status Code:</span>
                            <span class="detail-value">${status.statusCode || 'N/A'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Message:</span>
                            <span class="detail-value">${status.message || 'No message available'}</span>
                        </div>
                        ${status.resultExplanation ? `
                        <div class="detail-row">
                            <span class="detail-label">Details:</span>
                            <span class="detail-value">${status.resultExplanation}</span>
                        </div>
                        ` : ''}
                        ${status.fraudAlert ? `
                        <div class="detail-row">
                            <span class="detail-label">Fraud Alert:</span>
                            <span class="detail-value">${status.fraudAlert}</span>
                        </div>
                        ` : ''}
                        <div class="detail-row">
                            <span class="detail-label">Last Checked:</span>
                            <span class="detail-value">${new Date().toLocaleString()}</span>
                        </div>
                    </div>
                `;
                
                // Show appropriate alert based on status
                showStatusAlert(status.transactionStatus);
            }

            function addToHistory(status) {
                const historyEntry = {
                    timestamp: new Date(),
                    status: status.transactionStatus,
                    message: status.message,
                    statusCode: status.statusCode
                };
                
                // Add to beginning of array and limit to last 10 entries
                statusHistory.unshift(historyEntry);
                statusHistory = statusHistory.slice(0, 10);
                
                // Update history display
                updateHistoryDisplay();
            }

            function updateHistoryDisplay() {
                const historyContent = document.getElementById('history-content');
                
                if (statusHistory.length === 0) {
                    historyContent.innerHTML = '<p>No status checks yet.</p>';
                    return;
                }
                
                const historyHtml = statusHistory.map(entry => {
                    const statusClass = getStatusClass(entry.status);
                    const statusIcon = getStatusIcon(entry.status);
                    
                    return `
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid var(--gray-200); font-size: 14px;">
                            <div>
                                <span class="status-indicator ${statusClass}" style="font-size: 12px; padding: 4px 8px;">
                                    ${statusIcon} ${entry.status}
                                </span>
                                <span style="margin-left: 10px; color: var(--gray-600);">
                                    ${entry.message}
                                </span>
                            </div>
                            <div style="color: var(--gray-500); font-size: 12px;">
                                ${entry.timestamp.toLocaleTimeString()}
                            </div>
                        </div>
                    `;
                }).join('');
                
                historyContent.innerHTML = historyHtml;
            }

            function getStatusClass(status) {
                switch (status) {
                    case 'APPROVED': return 'status-approved';
                    case 'DECLINED': return 'status-declined';
                    case 'CANCELLED': return 'status-cancelled';
                    default: return 'status-pending';
                }
            }

            function getStatusIcon(status) {
                switch (status) {
                    case 'APPROVED': return '✅';
                    case 'DECLINED': return '❌';
                    case 'CANCELLED': return '🚫';
                    case 'PENDING': return '⏳';
                    default: return '❓';
                }
            }

            function showStatusAlert(status) {
                switch (status) {
                    case 'APPROVED':
                        window.paymentClient.showAlert('Payment completed successfully!', 'success');
                        break;
                    case 'DECLINED':
                        window.paymentClient.showAlert('Payment was declined. Please check the details and try again.', 'error');
                        break;
                    case 'CANCELLED':
                        window.paymentClient.showAlert('Payment was cancelled.', 'warning');
                        break;
                    case 'PENDING':
                        window.paymentClient.showAlert('Payment is still being processed. Please wait or check again later.', 'info');
                        break;
                    default:
                        window.paymentClient.showAlert('Status retrieved successfully.', 'info');
                }
            }

            function toggleAutoRefresh() {
                const btn = document.getElementById('auto-refresh-btn');
                
                if (autoRefreshInterval) {
                    // Stop auto-refresh
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                    btn.innerHTML = 'Enable Auto-Refresh';
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-secondary');
                    window.paymentClient.showAlert('Auto-refresh disabled', 'info');
                } else {
                    // Start auto-refresh
                    const token = document.getElementById('transactionToken').value.trim();
                    if (!token) {
                        window.paymentClient.showAlert('Please enter a transaction token first', 'warning');
                        return;
                    }
                    
                    autoRefreshInterval = setInterval(() => {
                        checkPaymentStatus();
                    }, 15000); // Refresh every 15 seconds
                    
                    btn.innerHTML = 'Disable Auto-Refresh';
                    btn.classList.remove('btn-secondary');
                    btn.classList.add('btn-primary');
                    window.paymentClient.showAlert('Auto-refresh enabled (every 15 seconds)', 'success');
                }
            }

            async function copyTokenToClipboard() {
                const token = document.getElementById('transactionToken').value.trim();
                if (!token) {
                    window.paymentClient.showAlert('No token to copy', 'warning');
                    return;
                }
                
                try {
                    await navigator.clipboard.writeText(token);
                    window.paymentClient.showAlert('Transaction token copied to clipboard', 'success');
                } catch (error) {
                    console.error('Failed to copy token:', error);
                    window.paymentClient.showAlert('Failed to copy token to clipboard', 'error');
                }
            }

            // Clean up auto-refresh when page is unloaded
            window.addEventListener('beforeunload', function() {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
            });
        });
    </script>
</body>
</html>
