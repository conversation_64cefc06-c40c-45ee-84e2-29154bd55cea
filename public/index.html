<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Secure and reliable payment processing for Malawi</p>
        </div>

        <div id="alert-container"></div>

        <div class="card">
            <h2>Welcome to NewWave Payments</h2>
            <p>Choose your preferred payment method to get started. Our secure payment gateway supports mobile money and credit card payments across Malawi.</p>
            
            <div class="payment-methods">
                <a href="/payment-form.html" class="payment-method">
                    <div class="payment-method-icon">💳</div>
                    <h3>Start New Payment</h3>
                    <p>Initiate a new payment transaction with customer details and service information</p>
                </a>

                <a href="/payment-status.html" class="payment-method">
                    <div class="payment-method-icon">📊</div>
                    <h3>Check Payment Status</h3>
                    <p>Track the status of an existing payment using your transaction token</p>
                </a>

                <a href="/mobile-money.html" class="payment-method">
                    <div class="payment-method-icon">📱</div>
                    <h3>Mobile Money</h3>
                    <p>Pay using Airtel Money or TNM Mpamba mobile money services</p>
                </a>

                <a href="/card-payment.html" class="payment-method">
                    <div class="payment-method-icon">💳</div>
                    <h3>Credit Card</h3>
                    <p>Pay securely using your Visa, Mastercard, or other supported credit cards</p>
                </a>
            </div>
        </div>

        <div class="card">
            <h3>Supported Payment Methods</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">📱</div>
                    <h4>Airtel Money</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Mobile money payments</p>
                </div>
                <div style="text-align: center; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">💰</div>
                    <h4>TNM Mpamba</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Mobile money payments</p>
                </div>
                <div style="text-align: center; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">💳</div>
                    <h4>Visa</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Credit & debit cards</p>
                </div>
                <div style="text-align: center; padding: 15px; background: var(--gray-50); border-radius: 8px;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">💳</div>
                    <h4>Mastercard</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Credit & debit cards</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>How It Works</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center;">
                    <div style="width: 60px; height: 60px; background: var(--primary-color); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold; margin: 0 auto 15px;">1</div>
                    <h4>Initiate Payment</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Fill in payment details including amount, currency, and customer information</p>
                </div>
                <div style="text-align: center;">
                    <div style="width: 60px; height: 60px; background: var(--primary-color); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold; margin: 0 auto 15px;">2</div>
                    <h4>Choose Payment Method</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Select between mobile money or credit card payment options</p>
                </div>
                <div style="text-align: center;">
                    <div style="width: 60px; height: 60px; background: var(--primary-color); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold; margin: 0 auto 15px;">3</div>
                    <h4>Complete Payment</h4>
                    <p style="font-size: 14px; color: var(--gray-600);">Follow the prompts to complete your secure payment transaction</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>Security & Trust</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="font-size: 2rem;">🔒</div>
                    <div>
                        <h4>SSL Encrypted</h4>
                        <p style="font-size: 14px; color: var(--gray-600);">All transactions are secured with 256-bit SSL encryption</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="font-size: 2rem;">🛡️</div>
                    <div>
                        <h4>PCI Compliant</h4>
                        <p style="font-size: 14px; color: var(--gray-600);">Meets international payment security standards</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="font-size: 2rem;">⚡</div>
                    <div>
                        <h4>Real-time Processing</h4>
                        <p style="font-size: 14px; color: var(--gray-600);">Instant payment processing and status updates</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="font-size: 2rem;">📞</div>
                    <div>
                        <h4>24/7 Support</h4>
                        <p style="font-size: 14px; color: var(--gray-600);">Round-the-clock customer support available</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card" style="text-align: center; background: var(--gray-50);">
            <h3>Need Help?</h3>
            <p>If you encounter any issues or need assistance with your payment, please contact our support team.</p>
            <div style="margin-top: 20px;">
                <a href="mailto:<EMAIL>" class="btn btn-primary">Contact Support</a>
                <a href="/docs/payment-gateway-guide.md" class="btn btn-secondary" style="margin-left: 10px;">View Documentation</a>
            </div>
        </div>
    </div>

    <footer style="text-align: center; padding: 40px 20px; color: var(--gray-600); border-top: 1px solid var(--gray-200); margin-top: 40px;">
        <p>&copy; 2024 NewWave Payment Gateway. All rights reserved.</p>
        <p style="font-size: 14px; margin-top: 10px;">
            <a href="#" style="color: var(--gray-600); text-decoration: none; margin: 0 10px;">Privacy Policy</a>
            <a href="#" style="color: var(--gray-600); text-decoration: none; margin: 0 10px;">Terms of Service</a>
            <a href="#" style="color: var(--gray-600); text-decoration: none; margin: 0 10px;">API Documentation</a>
        </p>
    </footer>

    <script src="/js/payment-client.js"></script>
    <script>
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if there are any URL parameters for alerts
            const urlParams = new URLSearchParams(window.location.search);
            const message = urlParams.get('message');
            const type = urlParams.get('type');
            
            if (message) {
                window.paymentClient.showAlert(decodeURIComponent(message), type || 'info');
            }
        });
    </script>
</body>
</html>
