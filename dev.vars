# Database Configuration
# Set DB_TYPE to 'turso' to use Turso database
DB_TYPE=turso

# Turso Database Configuration
TURSO_DATABASE_URL=libsql://your-database-name.turso.io
TURSO_AUTH_TOKEN=your_auth_token

# SQLite Configuration (fallback)
SQLITE_DB_PATH=./database/sqlite.db

# PostgreSQL Configuration (if needed)
DATABASE_URL=postgres://user:password@localhost:5432/dbname
DB_USER=user
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dbname

JWT_SECRET=
SIGNING_SECRET=

ALLOWED_IPS="https://newwave-payment-gateway.newwavemalawi.workers.dev"
BACK_URL="https://newwave-payment-gateway.newwavemalawi.workers.dev/payment/back"
DPO_BASE_URL="https://secure.3gdirectpay.com/API/v6/"
DPO_COMPANY_TOKEN="8D3DA73D-9D7F-4E09-96D4-3D44E7A83EA3"
DPO_PAYMENT_URL="https://secure.3gdirectpay.com/payv3.php"
ENVIRONMENT="production"
JWT_ACCESS_TOKEN_EXPIRY=900
JWT_AUDIENCE="payment-api"
JWT_ISSUER="newwave-payment-gateway"
JWT_REFRESH_TOKEN_EXPIRY=604800
JWT_SUBJECT="api-client"
WEBHOOK_URL="https://newwave-payment-gateway.newwavemalawi.workers.dev/api/payments/webhook"
