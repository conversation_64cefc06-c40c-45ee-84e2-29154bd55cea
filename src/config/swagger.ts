export const swaggerOptions = {
  openapi: '3.0.0',
  info: {
    title: 'Newwave Payment Gateway API',
    version: '1.0.0',
    description: `
    # Newwave Payment Gateway API

    A secure and reliable payment processing service that supports multiple payment methods including credit cards and mobile money.

    ## Features
    - Credit Card Processing
    - Mobile Money Integration
    - Real-time Payment Status
    - Secure Transaction Handling
    - JWT Authentication with Refresh Tokens
    - Rate Limiting

    ## Authentication
    All API requests require authentication using JWT tokens. Include your JWT token in the Authorization header with every request:

    \`\`\`
    Authorization: Bearer <your-jwt-token>
    \`\`\`

    ## Token Management

    ### Access Tokens
    - Valid for 15 minutes
    - Used for API requests
    - Rate limited to 100 requests per 15 minutes

    ### Refresh Tokens
    - Valid for 7 days
    - Used to obtain new access tokens
    - Rate limited to 10 requests per 15 minutes

    ### Token Revocation
    You can revoke tokens before expiration using the \`/api/auth/revoke\` endpoint.

    ## Rate Limiting
    - Access tokens: 100 requests per 15 minutes
    - Refresh tokens: 10 requests per 15 minutes
    - Rate limit headers included in responses:
    - \`X-RateLimit-Limit\`: Maximum requests allowed
    - \`X-RateLimit-Remaining\`: Remaining requests
    - \`X-RateLimit-Reset\`: Time until limit resets

    ## Generating JWT Tokens

    To generate a JWT token for API requests:

    1. Set the JWT secret (if not already set in .dev.vars):
    \`\`\`bash
    export JWT_SECRET=test_jwt_secret_key_123
    \`\`\`

    2. Use the provided npm script:
    \`\`\`bash
    pnpm generate-token '{
    "transaction": {
    "paymentAmount": 1000,
    "paymentCurrency": "MWK",
    "companyRef": "NW-2024-0507-001",
    "redirectURL": "https://dev.newwave.com/payment/success",
    "backURL": "https://dev.newwave.com/payment/cancel",
    "customer": {
        "firstName": "John",
        "lastName": "Banda",
        "email": "<EMAIL>",
        "phone": "+265994567890"
      }
    },
    "services": [
    {
      "serviceType": "5525",
      "serviceDescription": "Internet Data Bundle",
      "serviceDate": "2024-05-07",
      "serviceFrom": "LIL",
      "serviceTo": "LIL"
    }
    ]
    }'
    \`\`\`

    3. The script will generate a JWT token that you can use in the Authorization header
    4. For local development, the JWT secret is automatically loaded from \`.dev.vars\`
    5. For production, set the JWT secret using \`wrangler secret put JWT_SECRET\`

    Note: Make sure to properly escape the JSON string when using it in the command line.
    `,
      contact: {
        name: 'API Support',
        url: 'https://support.newwave.com',
        email: '<EMAIL>'
      },
      license: {
        name: 'Proprietary',
        url: 'https://newwave.com/terms'
      }
    },
    servers: [
    {
      url: 'http://127.0.0.1:5173',
      description: 'Local Development'
    },
    {
      url: 'https://newwave-payment-gateway.newwavemalawi.workers.dev/',
      description: 'UAT'
    },
    {
      url: 'https://pay.newwave.com',
      description: 'Production'
    }
  ],
  tags: [
    {
      name: 'payments',
      description: 'Payment processing endpoints'
    },
    {
      name: 'status',
      description: 'Status and health check endpoints'
    },
    {
      name: 'auth',
      description: 'Authentication and token management'
    }
  ],
  components: {
    securitySchemes: {
      BearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token for API authentication. Include the token in the Authorization header as: Bearer <token>'
      }
    },
    schemas: {
      PaymentRequest: {
        type: 'object',
        required: ['transaction', 'services'],
        properties: {
          transaction: {
            type: 'object',
            required: ['paymentAmount', 'paymentCurrency', 'companyRef', 'redirectURL', 'backURL', 'customer'],
            properties: {
              paymentAmount: {
                type: 'number',
                description: 'Payment amount'
              },
              paymentCurrency: {
                type: 'string',
                description: 'Payment currency code'
              },
              companyRef: {
                type: 'string',
                description: 'Company reference number'
              },
              redirectURL: {
                type: 'string',
                format: 'uri',
                description: 'Success redirect URL'
              },
              backURL: {
                type: 'string',
                format: 'uri',
                description: 'Cancel redirect URL'
              },
              customer: {
                type: 'object',
                required: ['firstName', 'lastName', 'email', 'phone'],
                properties: {
                  firstName: {
                    type: 'string',
                    description: 'Customer first name'
                  },
                  lastName: {
                    type: 'string',
                    description: 'Customer last name'
                  },
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'Customer email'
                  },
                  phone: {
                    type: 'string',
                    description: 'Customer phone number'
                  }
                }
              }
            }
          },
          services: {
            type: 'array',
            items: {
              type: 'object',
              required: ['serviceType', 'serviceDescription', 'serviceDate', 'serviceFrom', 'serviceTo'],
              properties: {
                serviceType: {
                  type: 'string',
                  description: 'Service type code'
                },
                serviceDescription: {
                  type: 'string',
                  description: 'Service description'
                },
                serviceDate: {
                  type: 'string',
                  format: 'date',
                  description: 'Service date'
                },
                serviceFrom: {
                  type: 'string',
                  format: 'date',
                  description: 'Service start date'
                },
                serviceTo: {
                  type: 'string',
                  format: 'date',
                  description: 'Service end date'
                }
              }
            }
          }
        }
      },
      PaymentResponse: {
        type: 'object',
        required: ['status', 'transactionId', 'amount', 'currency', 'timestamp'],
        properties: {
          status: {
            type: 'string',
            description: 'Payment status'
          },
          transactionId: {
            type: 'string',
            description: 'Unique transaction ID'
          },
          amount: {
            type: 'number',
            description: 'Payment amount'
          },
          currency: {
            type: 'string',
            description: 'Payment currency'
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Transaction timestamp'
          }
        }
      },
      StatusResponse: {
        type: 'object',
        required: ['transactionId', 'status', 'timestamp'],
        properties: {
          transactionId: {
            type: 'string',
            description: 'Transaction ID'
          },
          status: {
            type: 'string',
            description: 'Payment status'
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Status check timestamp'
          }
        }
      },
      ErrorResponse: {
        type: 'object',
        required: ['error'],
        properties: {
          error: {
            type: 'string',
            description: 'Error message'
          }
        }
      },
      TokenResponse: {
        type: 'object',
        required: ['access_token', 'refresh_token', 'token_type', 'expires_in'],
        properties: {
          access_token: {
            type: 'string',
            description: 'JWT access token'
          },
          refresh_token: {
            type: 'string',
            description: 'JWT refresh token'
          },
          token_type: {
            type: 'string',
            description: 'Token type (Bearer)'
          },
          expires_in: {
            type: 'integer',
            description: 'Token expiration time in seconds'
          }
        }
      }
    }
  },
  security: [
    {
      BearerAuth: []
    }
  ]
} 