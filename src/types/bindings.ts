export interface Env {
  // DPO Payment Gateway credentials
  DPO_COMPANY_TOKEN: string;
  DPO_PAYMENT_URL: string;
  DPO_BASE_URL: string;
  
  // Webhook and redirect URLs
  WEBHOOK_URL: string;
  BACK_URL: string;
  
  // JWT Authentication
  JWT_SECRET: string;  // Secret key for JWT token signing/verification
  JWT_ISSUER?: string; // Optional: JWT issuer (defaults to 'newwave-payment-gateway')
  JWT_AUDIENCE?: string; // Optional: JWT audience (defaults to 'payment-api')
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS?: number; // Optional: Rate limit window in milliseconds (defaults to 15 minutes)
  RATE_LIMIT_MAX_REQUESTS?: number; // Optional: Max requests per window (defaults to 100)
  RATE_LIMIT_REFRESH_MAX_REQUESTS?: number; // Optional: Max refresh token requests (defaults to 10)

  // Database Configuration
  DB_TYPE?: 'sqlite' | 'postgres' | 'd1'; // Database type (defaults to 'sqlite')
  
  // SQLite Configuration
  SQLITE_DB_PATH?: string; // Path to SQLite database file (defaults to ':memory:')
  
  // PostgreSQL Configuration
  DATABASE_URL?: string; // PostgreSQL connection string
  DB_HOST?: string; // PostgreSQL host
  DB_PORT?: number; // PostgreSQL port
  DB_NAME?: string; // PostgreSQL database name
  DB_USER?: string; // PostgreSQL username
  DB_PASSWORD?: string; // PostgreSQL password

  TURSO_DATABASE_URL: string;
  TURSO_AUTH_TOKEN?: string;
  
  
  // Cloudflare D1 Database
  DB?: any; // D1 database binding for Cloudflare Workers
  
  // Cloudflare Assets binding
  ASSETS: {
    fetch: (request: Request) => Promise<Response>;
  };

  // Application Environment
  ENVIRONMENT?: string; // Added for logger compatibility and general use
}