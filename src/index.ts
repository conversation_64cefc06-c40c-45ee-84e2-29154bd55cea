import { Hono } from 'hono';
import { OpenAPIHono } from '@hono/zod-openapi';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger as honoRequestLogger } from 'hono/logger';
import { timeout } from 'hono/timeout';
import { swaggerUI } from '@hono/swagger-ui';

import { authMiddleware, refreshTokenAuth, generateTokens } from './middleware/auth';
import { jwtRateLimiter } from './middleware/jwtRateLimiter';
import { rateLimiter } from './middleware/rateLimiter';
import { getLogger } from './utils/logger';

import { securityMiddleware } from './middleware/security';
import { timeoutMiddleware } from './middleware/timeout';
import { loggerMiddleware } from './middleware/logger';
import { DatabaseService } from './db';
import paymentRoutes from './routes/payment';
import dashboardRoutes from './routes/dashboard';
import webhookRoutes from './routes/webhook';
import statusRoutes from './routes/status';
import type { Env } from './types/bindings';

// Extend the globalThis type to include our custom properties
declare global {
  // eslint-disable-next-line no-var
  var app: Hono<{ Bindings: Env }> | undefined;
  // eslint-disable-next-line no-var
  var apiRouter: OpenAPIHono<{ Bindings: Env }> | undefined;
  // eslint-disable-next-line no-var
  var workerApp: Hono<{ Bindings: Env }> | undefined;
}

// Create main application instance
let app: Hono<{ Bindings: Env }>;

// Only create the app once to avoid duplicate handlers
if (!globalThis.app) {
  app = new Hono<{ Bindings: Env }>();
  
  // Apply global middleware
  app.use('*', timeoutMiddleware({ 
    timeout: 30000, // 30 seconds
    message: 'Request processing timeout'
  }));
  
  // Apply CORS middleware
  app.use('*', cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposeHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
    maxAge: 86400,
    credentials: true
  }));
  
  app.use('*', secureHeaders());
  app.use('*', securityMiddleware);
  app.use('*', honoRequestLogger());
  app.use('*', loggerMiddleware);
  
  // Store the app in the global scope
  globalThis.app = app;
} else {
  // Use the existing app instance
  app = globalThis.app as Hono<{ Bindings: Env }>;
}

// Create API router if it doesn't exist
let api: OpenAPIHono<{ Bindings: Env }>;

if (!globalThis.apiRouter) {
  api = new OpenAPIHono<{ Bindings: Env }>();
  
  // Apply rate limiting to API routes
  api.use('*', rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
  }));
  
  globalThis.apiRouter = api;
} else {
  api = globalThis.apiRouter as OpenAPIHono<{ Bindings: Env }>;
}

// Initialize database on application startup
api.use('*', async (c, next) => {
  const log = getLogger(c);
  log.info('Initializing database connection');
  
  try {
    const dbService = new DatabaseService(c.env, log, c.env.DB_TYPE as any || 'sqlite');
    await dbService.connect();
    
    log.info('Database connection initialized successfully');
    
    return next();
  } catch (error: any) {
    log.error('Failed to initialize database connection:', error);
    return c.json({ error: 'Database connection failed', details: error.message }, 500);
  }
});

// Apply authentication and rate limiting to API routes
api.use('*', rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests from this IP, please try again later.',
}));

// Apply JWT auth and rate limiting to protected routes
api.use('/payment/*', authMiddleware());
api.use('/status/*', authMiddleware());

// Auth routes
api.post('/auth/refresh', refreshTokenAuth, async (c) => {
  const payload = c.get('jwtPayload');
  const env = c.env;

  // Generate new tokens
  const { accessToken, refreshToken } = await generateTokens(payload, env.JWT_SECRET);

  return c.json({
    access_token: accessToken,
    refresh_token: refreshToken,
    token_type: 'Bearer',
    expires_in: 900 // 15 minutes
  });
});

// Mount API routes
api.route('/payment', paymentRoutes);
api.route('/dashboard', dashboardRoutes);
api.route('/webhooks', webhookRoutes);
api.route('/status', statusRoutes);

// Mount API router to main application
app.route('/api', api);

// Simple static content handler
app.get('/', async (c) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NewWave Payment Gateway</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f9fafb; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .card { background: white; border-radius: 12px; padding: 30px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .btn { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 8px; margin: 10px; }
        .btn:hover { background: #1d4ed8; }
        .payment-methods { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 30px; }
        .payment-method { background: white; border: 2px solid #e5e7eb; border-radius: 12px; padding: 25px; text-align: center; text-decoration: none; color: inherit; }
        .payment-method:hover { border-color: #2563eb; transform: translateY(-2px); }
        .payment-method-icon { font-size: 3rem; margin-bottom: 15px; color: #2563eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Secure and reliable payment processing for Malawi</p>
        </div>

        <div class="card">
            <h2>Welcome to NewWave Payments</h2>
            <p>Your payment gateway is running successfully! The API is ready to process payments.</p>

            <h3>Available API Endpoints:</h3>
            <ul style="text-align: left; line-height: 1.8;">
                <li><strong>POST /api/payment/initiate</strong> - Initiate a new payment</li>
                <li><strong>POST /api/payment/mobile-money</strong> - Process mobile money payment</li>
                <li><strong>POST /api/payment/card</strong> - Process credit card payment</li>
                <li><strong>GET /api/payment/status/{token}</strong> - Check payment status</li>
                <li><strong>POST /api/webhooks/dpo</strong> - DPO webhook receiver</li>
            </ul>

            <div style="margin-top: 30px; text-align: center;">
                <a href="/docs" class="btn">📖 API Documentation</a>
                <a href="/api/payment/status/test" class="btn">🧪 Test API</a>
            </div>
        </div>

        <div class="card">
            <h3>Payment Methods Supported</h3>
            <div class="payment-methods">
                <div class="payment-method">
                    <div class="payment-method-icon">📱</div>
                    <h4>Airtel Money</h4>
                    <p>Mobile money payments</p>
                </div>
                <div class="payment-method">
                    <div class="payment-method-icon">💰</div>
                    <h4>TNM Mpamba</h4>
                    <p>Mobile money payments</p>
                </div>
                <div class="payment-method">
                    <div class="payment-method-icon">💳</div>
                    <h4>Credit Cards</h4>
                    <p>Visa, Mastercard, etc.</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>🔧 For Developers</h3>
            <p>To integrate with the payment gateway:</p>
            <ol style="line-height: 1.8;">
                <li>Generate a JWT token using your payment data</li>
                <li>Call the <code>/api/payment/initiate</code> endpoint</li>
                <li>Use the returned transaction token for payment processing</li>
                <li>Monitor payment status via webhooks or status endpoint</li>
            </ol>

            <p><strong>Note:</strong> The complete payment UI forms are available in the <code>public/</code> directory.
            For full frontend functionality, serve these files through a web server or CDN.</p>
        </div>
    </div>
</body>
</html>`;

  return c.html(html);
});

// Serve basic payment form
app.get('/payment-form', async (c) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Form - NewWave</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f9fafb; }
        .container { max-width: 600px; margin: 0 auto; }
        .card { background: white; border-radius: 12px; padding: 30px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-group input, .form-group select { width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; }
        .btn { background: #2563eb; color: white; padding: 12px 24px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; }
        .btn:hover { background: #1d4ed8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🌊 Payment Form</h1>
            <p>This is a basic payment form. For the complete payment UI with client-side JWT generation,
            please serve the files from the <code>public/</code> directory.</p>

            <form>
                <div class="form-group">
                    <label>Amount (MWK)</label>
                    <input type="number" placeholder="15000.00" required>
                </div>
                <div class="form-group">
                    <label>Customer Name</label>
                    <input type="text" placeholder="John Banda" required>
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label>Phone</label>
                    <input type="tel" placeholder="+265994567890" required>
                </div>
                <button type="button" class="btn" onclick="alert('Use the API endpoints directly or serve the complete UI from public/ directory')">
                    Initiate Payment
                </button>
            </form>

            <div style="margin-top: 30px; padding: 20px; background: #f3f4f6; border-radius: 8px;">
                <h4>API Integration</h4>
                <p>To process payments programmatically, use the API endpoints:</p>
                <ul>
                    <li>POST /api/payment/initiate</li>
                    <li>POST /api/payment/mobile-money</li>
                    <li>POST /api/payment/card</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>`;

  return c.html(html);
});

// OpenAPI documentation
app.get('/docs', swaggerUI({ url: '/docs/openapi.json' }));

// Error handling
app.onError((err, c) => {
  const log = getLogger(c);
  log.error('Unhandled application error:', err);
  
  return c.json({
    error: 'Internal Server Error',
    message: err.message,
    stack: c.env.ENVIRONMENT === 'development' ? err.stack : undefined
  }, 500);
});

// Ensure we only export the app once
if (!globalThis.workerApp) {
  globalThis.workerApp = app;
}

export default globalThis.workerApp;