import { Hono } from 'hono';
import { OpenAPIHono } from '@hono/zod-openapi';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger as honoRequestLogger } from 'hono/logger';
import { timeout } from 'hono/timeout';
import { swaggerUI } from '@hono/swagger-ui';

import { authMiddleware, refreshTokenAuth, generateTokens } from './middleware/auth';
import { jwtRateLimiter } from './middleware/jwtRateLimiter';
import { rateLimiter } from './middleware/rateLimiter';
import { getLogger } from './utils/logger';

import { securityMiddleware } from './middleware/security';
import { timeoutMiddleware } from './middleware/timeout';
import { loggerMiddleware } from './middleware/logger';
import { DatabaseService } from './db';
import paymentRoutes from './routes/payment';
import dashboardRoutes from './routes/dashboard';
import webhookRoutes from './routes/webhook';
import statusRoutes from './routes/status';
import type { Env } from './types/bindings';

// Extend the globalThis type to include our custom properties
declare global {
  // eslint-disable-next-line no-var
  var app: Hono<{ Bindings: Env }> | undefined;
  // eslint-disable-next-line no-var
  var apiRouter: OpenAPIHono<{ Bindings: Env }> | undefined;
  // eslint-disable-next-line no-var
  var workerApp: Hono<{ Bindings: Env }> | undefined;
}

// Create main application instance
let app: Hono<{ Bindings: Env }>;

// Only create the app once to avoid duplicate handlers
if (!globalThis.app) {
  app = new Hono<{ Bindings: Env }>();
  
  // Apply global middleware
  app.use('*', timeoutMiddleware({ 
    timeout: 30000, // 30 seconds
    message: 'Request processing timeout'
  }));
  
  // Apply CORS middleware
  app.use('*', cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposeHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
    maxAge: 86400,
    credentials: true
  }));
  
  app.use('*', secureHeaders());
  app.use('*', securityMiddleware);
  app.use('*', honoRequestLogger());
  app.use('*', loggerMiddleware);
  
  // Store the app in the global scope
  globalThis.app = app;
} else {
  // Use the existing app instance
  app = globalThis.app as Hono<{ Bindings: Env }>;
}

// Create API router if it doesn't exist
let api: OpenAPIHono<{ Bindings: Env }>;

if (!globalThis.apiRouter) {
  api = new OpenAPIHono<{ Bindings: Env }>();
  
  // Apply rate limiting to API routes
  api.use('*', rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
  }));
  
  globalThis.apiRouter = api;
} else {
  api = globalThis.apiRouter as OpenAPIHono<{ Bindings: Env }>;
}

// Initialize database on application startup
api.use('*', async (c, next) => {
  const log = getLogger(c);
  log.info('Initializing database connection');
  
  try {
    const dbService = new DatabaseService(c.env, log, c.env.DB_TYPE as any || 'sqlite');
    await dbService.connect();
    
    log.info('Database connection initialized successfully');
    
    return next();
  } catch (error: any) {
    log.error('Failed to initialize database connection:', error);
    return c.json({ error: 'Database connection failed', details: error.message }, 500);
  }
});

// Apply authentication and rate limiting to API routes
api.use('*', rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests from this IP, please try again later.',
}));

// Apply JWT auth and rate limiting to protected routes
api.use('/payment/*', authMiddleware());
api.use('/status/*', authMiddleware());

// Auth routes
api.post('/auth/refresh', refreshTokenAuth, async (c) => {
  const payload = c.get('jwtPayload');
  const env = c.env;

  // Generate new tokens
  const { accessToken, refreshToken } = await generateTokens(payload, env.JWT_SECRET);

  return c.json({
    access_token: accessToken,
    refresh_token: refreshToken,
    token_type: 'Bearer',
    expires_in: 900 // 15 minutes
  });
});

// Mount API routes
api.route('/payment', paymentRoutes);
api.route('/dashboard', dashboardRoutes);
api.route('/webhooks', webhookRoutes);
api.route('/status', statusRoutes);

// Mount API router to main application
app.route('/api', api);

// Serve payment pages with embedded content
app.get('/', (c) => {
  return c.redirect('/index.html');
});

app.get('/index.html', (c) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Secure and reliable payment processing for Malawi</p>
        </div>

        <div id="alert-container"></div>

        <div class="card">
            <h2>Welcome to NewWave Payments</h2>
            <p>Choose your preferred payment method to get started. Our secure payment gateway supports mobile money and credit card payments across Malawi.</p>

            <div class="payment-methods">
                <a href="/payment-form.html" class="payment-method">
                    <div class="payment-method-icon">💳</div>
                    <h3>Start New Payment</h3>
                    <p>Initiate a new payment transaction with customer details and service information</p>
                </a>

                <a href="/payment-status.html" class="payment-method">
                    <div class="payment-method-icon">📊</div>
                    <h3>Check Payment Status</h3>
                    <p>Track the status of an existing payment using your transaction token</p>
                </a>

                <a href="/mobile-money.html" class="payment-method">
                    <div class="payment-method-icon">📱</div>
                    <h3>Mobile Money</h3>
                    <p>Pay using Airtel Money or TNM Mpamba mobile money services</p>
                </a>

                <a href="/card-payment.html" class="payment-method">
                    <div class="payment-method-icon">💳</div>
                    <h3>Credit Card</h3>
                    <p>Pay securely using your Visa, Mastercard, or other supported credit cards</p>
                </a>
            </div>
        </div>

        <div class="card">
            <h3>🔧 For Developers</h3>
            <p>API Endpoints available:</p>
            <ul style="line-height: 1.8;">
                <li><strong>POST /api/payment/initiate</strong> - Initiate payment</li>
                <li><strong>POST /api/payment/mobile-money</strong> - Mobile money payment</li>
                <li><strong>POST /api/payment/card</strong> - Card payment</li>
                <li><strong>GET /api/payment/status/{token}</strong> - Check status</li>
                <li><strong>POST /api/webhooks/dpo</strong> - DPO webhooks</li>
            </ul>
            <div style="margin-top: 20px;">
                <a href="/docs" class="btn btn-primary">📖 API Documentation</a>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
</body>
</html>`;
  return c.html(html);
});

// Serve CSS
app.get('/css/payment-styles.css', (c) => {
  const css = `/* NewWave Payment Gateway Styles */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-600: #4b5563;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header h1 {
  color: var(--primary-color);
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.header p {
  color: var(--gray-600);
  font-size: 1.1rem;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--gray-200);
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.payment-method {
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.payment-method:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-method-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.payment-method h3 {
  margin-bottom: 10px;
  color: var(--gray-800);
}

.payment-method p {
  color: var(--gray-600);
  font-size: 14px;
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .card {
    padding: 20px;
  }

  .payment-methods {
    grid-template-columns: 1fr;
  }
}`;

  return c.text(css, 200, { 'Content-Type': 'text/css' });
});

// Serve JavaScript
app.get('/js/payment-client.js', (c) => {
  const js = `// NewWave Payment Gateway Client-Side JavaScript
class PaymentClient {
  constructor() {
    this.apiBaseUrl = window.location.origin + '/api';
    this.jwtSecret = 'test_jwt_secret_key_123';
    this.currentToken = null;
  }

  async generateJWT(payload) {
    const jwtConfig = {
      issuer: 'newwave-payment-gateway',
      subject: 'payment-client',
      audience: 'newwave-api',
      accessTokenExpiry: 900
    };

    const basePayload = {
      iss: jwtConfig.issuer,
      sub: jwtConfig.subject,
      aud: jwtConfig.audience,
      iat: Math.floor(Date.now() / 1000),
      jti: this.generateUUID(),
      request: payload,
      timestamp: Date.now(),
      type: 'access',
      exp: Math.floor(Date.now() / 1000) + jwtConfig.accessTokenExpiry
    };

    return await this.signJWT(basePayload, this.jwtSecret);
  }

  async signJWT(payload, secret) {
    const header = { alg: 'HS256', typ: 'JWT' };
    const encodedHeader = this.base64UrlEncode(JSON.stringify(header));
    const encodedPayload = this.base64UrlEncode(JSON.stringify(payload));
    const data = encodedHeader + '.' + encodedPayload;

    const signature = await this.hmacSHA256(data, secret);
    const encodedSignature = this.base64UrlEncode(signature);

    return data + '.' + encodedSignature;
  }

  async hmacSHA256(message, key) {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key);
    const messageData = encoder.encode(message);

    const cryptoKey = await crypto.subtle.importKey(
      'raw', keyData, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);
    return new Uint8Array(signature);
  }

  base64UrlEncode(data) {
    if (typeof data === 'string') {
      data = new TextEncoder().encode(data);
    }
    const base64 = btoa(String.fromCharCode(...data));
    return base64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');
  }

  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  async makeRequest(endpoint, method = 'GET', data = null, requiresAuth = true) {
    const url = this.apiBaseUrl + endpoint;
    const headers = { 'Content-Type': 'application/json' };

    if (requiresAuth && this.currentToken) {
      headers['Authorization'] = 'Bearer ' + this.currentToken;
    }

    const config = { method, headers };
    if (data) config.body = JSON.stringify(data);

    try {
      const response = await fetch(url, config);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'HTTP ' + response.status);
      }

      return result;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Process Mobile Money Payment
  async processMobileMoneyPayment(mobileMoneyData) {
    try {
      if (!this.currentToken) {
        throw new Error('No authentication token available');
      }

      const result = await this.makeRequest('/payment/mobile-money', 'POST', mobileMoneyData);
      return result;
    } catch (error) {
      console.error('Mobile money payment failed:', error);
      throw error;
    }
  }

  // Process Card Payment
  async processCardPayment(cardData) {
    try {
      if (!this.currentToken) {
        throw new Error('No authentication token available');
      }

      const result = await this.makeRequest('/payment/card', 'POST', cardData);
      return result;
    } catch (error) {
      console.error('Card payment failed:', error);
      throw error;
    }
  }

  // Check Payment Status
  async checkPaymentStatus(transactionToken) {
    try {
      if (!this.currentToken) {
        throw new Error('No authentication token available');
      }

      const result = await this.makeRequest('/payment/status/' + transactionToken, 'GET');
      return result;
    } catch (error) {
      console.error('Status check failed:', error);
      throw error;
    }
  }

  // Validation methods
  validateCardNumber(cardNumber) {
    // Basic Luhn algorithm check
    const cleaned = cardNumber.replace(/\\s/g, '');
    if (!/^[0-9]{13,19}$/.test(cleaned)) return false;

    let sum = 0;
    let isEven = false;

    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) digit -= 9;
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  validateCVV(cvv) {
    return /^[0-9]{3,4}$/.test(cvv);
  }

  validateExpiry(expiry) {
    const expiryRegex = /^(0[1-9]|1[0-2])\\/[0-9]{2}$/;
    if (!expiryRegex.test(expiry)) return false;

    const parts = expiry.split('/');
    const month = parseInt(parts[0]);
    const year = parseInt(parts[1]);
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;

    if (year < currentYear) return false;
    if (year === currentYear && month < currentMonth) return false;

    return true;
  }

  // UI Helper Functions
  showLoading(buttonElement, text = 'Processing...') {
    if (buttonElement) {
      buttonElement.disabled = true;
      buttonElement.innerHTML = text;
    }
  }

  hideLoading(buttonElement, originalText) {
    if (buttonElement) {
      buttonElement.disabled = false;
      buttonElement.innerHTML = originalText;
    }
  }

  showAlert(message, type = 'info') {
    alert(type.toUpperCase() + ': ' + message);
  }
}

window.paymentClient = new PaymentClient();
console.log('NewWave Payment Client loaded successfully');

// Utility functions for form handling
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return false;

  let isValid = true;
  const inputs = form.querySelectorAll('input[required], select[required]');

  inputs.forEach(input => {
    if (!input.value.trim()) {
      isValid = false;
      input.style.borderColor = '#ef4444';
    } else {
      input.style.borderColor = '#e5e7eb';
    }
  });

  return isValid;
}
`;

  return c.text(js, 200, { 'Content-Type': 'text/javascript' });
});

// Basic payment form
app.get('/payment-form.html', (c) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Form - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Initiate a new payment transaction</p>
        </div>

        <div class="card">
            <h2>Payment Details</h2>
            <form id="payment-form">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <h3 style="margin-bottom: 20px; color: var(--primary-color);">Transaction Information</h3>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Payment Amount (MWK) *</label>
                            <input type="number" id="paymentAmount" required min="1" step="0.01" placeholder="e.g., 15000.00"
                                   style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Currency *</label>
                            <select id="paymentCurrency" required style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                                <option value="MWK">MWK - Malawi Kwacha</option>
                                <option value="USD">USD - US Dollar</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Company Reference</label>
                            <input type="text" id="companyRef" placeholder="Auto-generated if empty"
                                   style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>

                    <div>
                        <h3 style="margin-bottom: 20px; color: var(--primary-color);">Customer Information</h3>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">First Name *</label>
                            <input type="text" id="firstName" required placeholder="e.g., John"
                                   style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Last Name *</label>
                            <input type="text" id="lastName" required placeholder="e.g., Banda"
                                   style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Email Address *</label>
                            <input type="email" id="email" required placeholder="e.g., <EMAIL>"
                                   style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">Phone Number *</label>
                            <input type="tel" id="phone" required placeholder="+265994567890"
                                   style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px;">
                        </div>
                    </div>
                </div>

                <div style="margin-top: 30px; display: flex; gap: 15px;">
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        Initiate Payment
                    </button>
                    <a href="/index.html" class="btn" style="background: var(--gray-200); color: var(--gray-800);">Cancel</a>
                </div>
            </form>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        document.getElementById('payment-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const paymentData = {
                transaction: {
                    paymentAmount: parseFloat(document.getElementById('paymentAmount').value),
                    paymentCurrency: document.getElementById('paymentCurrency').value,
                    companyRef: document.getElementById('companyRef').value || 'NW-' + Date.now(),
                    redirectURL: window.location.origin + '/success.html',
                    backURL: window.location.origin + '/cancel.html',
                    customer: {
                        firstName: document.getElementById('firstName').value,
                        lastName: document.getElementById('lastName').value,
                        email: document.getElementById('email').value,
                        phone: document.getElementById('phone').value
                    }
                },
                services: [{
                    serviceType: '5525',
                    serviceDescription: 'Internet Data Bundle',
                    serviceDate: new Date().toISOString().split('T')[0],
                    serviceFrom: 'LIL',
                    serviceTo: 'LIL'
                }]
            };

            try {
                const result = await window.paymentClient.generateJWT(paymentData);
                window.paymentClient.currentToken = result;

                const response = await window.paymentClient.makeRequest('/payment/initiate', 'POST', paymentData);

                alert('Payment initiated successfully! Transaction Token: ' + response.transactionToken);
                sessionStorage.setItem('transactionToken', response.transactionToken);

            } catch (error) {
                alert('Payment initiation failed: ' + error.message);
            }
        });
    </script>
</body>
</html>`;
  return c.html(html);
});

// Basic status page
app.get('/payment-status.html', (c) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Status - NewWave Payment Gateway</title>
    <link rel="stylesheet" href="/css/payment-styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌊 NewWave Payment Gateway</h1>
            <p>Check your payment transaction status</p>
        </div>

        <div class="card">
            <h2>📊 Payment Status Checker</h2>
            <p>Enter your transaction token to check the current status of your payment</p>

            <form id="status-form">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">Transaction Token *</label>
                    <input type="text" id="transactionToken" required placeholder="e.g., D0B6F0EA-9EAE-4399-B370-2CA0E49AED49"
                           style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 8px; font-size: 16px; font-family: monospace;">
                </div>

                <div style="display: flex; gap: 15px;">
                    <button type="submit" class="btn btn-primary">Check Payment Status</button>
                    <a href="/payment-form.html" class="btn" style="background: var(--gray-200); color: var(--gray-800);">New Payment</a>
                </div>
            </form>

            <div id="status-result" style="margin-top: 30px; display: none;">
                <h3>Payment Status</h3>
                <div id="status-content"></div>
            </div>
        </div>
    </div>

    <script src="/js/payment-client.js"></script>
    <script>
        // Auto-fill from session storage
        const token = sessionStorage.getItem('transactionToken');
        if (token) {
            document.getElementById('transactionToken').value = token;
        }

        document.getElementById('status-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const token = document.getElementById('transactionToken').value;
            if (!token) return;

            try {
                // Generate a simple token for status check
                window.paymentClient.currentToken = await window.paymentClient.generateJWT({});

                const result = await window.paymentClient.makeRequest('/payment/status/' + token);

                document.getElementById('status-content').innerHTML =
                    '<div style="background: var(--gray-50); padding: 20px; border-radius: 8px;">' +
                        '<p><strong>Status:</strong> ' + (result.transactionStatus || 'PENDING') + '</p>' +
                        '<p><strong>Message:</strong> ' + (result.message || 'Processing...') + '</p>' +
                        '<p><strong>Token:</strong> ' + (result.transToken || token) + '</p>' +
                        '<p><strong>Last Updated:</strong> ' + new Date().toLocaleString() + '</p>' +
                    '</div>';

                document.getElementById('status-result').style.display = 'block';

            } catch (error) {
                alert('Status check failed: ' + error.message);
            }
        });
    </script>
</body>
</html>`;
  return c.html(html);
});

// Mobile Money Payment Page
app.get('/mobile-money.html', async (c) => {
  try {
    // In development, try to read the actual file
    if (typeof process !== 'undefined' && process.versions?.node) {
      const fs = await import('fs/promises');
      const html = await fs.readFile('./public/mobile-money.html', 'utf-8');
      return c.html(html);
    }
  } catch (error) {
    console.error('Failed to read mobile-money.html:', error);
  }

  // Fallback to basic page
  return c.html(`<!DOCTYPE html>
<html><head><title>Mobile Money - NewWave</title><link rel="stylesheet" href="/css/payment-styles.css"></head>
<body><div class="container"><div class="header"><h1>🌊 Mobile Money Payment</h1></div>
<div class="card"><p>Mobile money payment page. Please use the complete version from /public/mobile-money.html</p>
<a href="/index.html" class="btn btn-primary">Back to Home</a></div></div></body></html>`);
});

// Card Payment Page
app.get('/card-payment.html', async (c) => {
  try {
    // In development, try to read the actual file
    if (typeof process !== 'undefined' && process.versions?.node) {
      const fs = await import('fs/promises');
      const html = await fs.readFile('./public/card-payment.html', 'utf-8');
      return c.html(html);
    }
  } catch (error) {
    console.error('Failed to read card-payment.html:', error);
  }

  // Fallback to basic page
  return c.html(`<!DOCTYPE html>
<html><head><title>Card Payment - NewWave</title><link rel="stylesheet" href="/css/payment-styles.css"></head>
<body><div class="container"><div class="header"><h1>🌊 Card Payment</h1></div>
<div class="card"><p>Card payment page. Please use the complete version from /public/card-payment.html</p>
<a href="/index.html" class="btn btn-primary">Back to Home</a></div></div></body></html>`);
});

app.get('/success.html', (c) => {
  return c.html(`<!DOCTYPE html>
<html><head><title>Payment Success - NewWave</title><link rel="stylesheet" href="/css/payment-styles.css"></head>
<body><div class="container"><div class="header"><h1>🌊 Payment Successful!</h1></div>
<div class="card"><p>Your payment has been processed successfully.</p>
<a href="/index.html" class="btn btn-primary">Back to Home</a></div></div></body></html>`);
});

app.get('/cancel.html', (c) => {
  return c.html(`<!DOCTYPE html>
<html><head><title>Payment Cancelled - NewWave</title><link rel="stylesheet" href="/css/payment-styles.css"></head>
<body><div class="container"><div class="header"><h1>🌊 Payment Cancelled</h1></div>
<div class="card"><p>Your payment was cancelled. No charges have been made.</p>
<a href="/index.html" class="btn btn-primary">Back to Home</a></div></div></body></html>`);
});

// OpenAPI JSON endpoint
app.get('/docs/openapi.json', (c) => {
  return c.json(api.getOpenAPIDocument({
    openapi: '3.0.0',
    info: {
      version: '1.0.0',
      title: 'NewWave Payment Gateway API',
      description: 'Secure payment processing API for Malawi'
    },
    servers: [
      {
        url: c.req.url.replace(/\/docs\/openapi\.json.*$/, ''),
        description: 'Payment Gateway API'
      }
    ]
  }));
});

// OpenAPI documentation
app.get('/docs', swaggerUI({ url: '/docs/openapi.json' }));

// Error handling
app.onError((err, c) => {
  const log = getLogger(c);
  log.error('Unhandled application error:', err);
  
  return c.json({
    error: 'Internal Server Error',
    message: err.message,
    stack: c.env.ENVIRONMENT === 'development' ? err.stack : undefined
  }, 500);
});

// Ensure we only export the app once
if (!globalThis.workerApp) {
  globalThis.workerApp = app;
}

export default globalThis.workerApp;