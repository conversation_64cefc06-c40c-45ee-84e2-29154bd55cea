import { Hono } from 'hono';
import { OpenAPIHono } from '@hono/zod-openapi';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger as honoRequestLogger } from 'hono/logger';
import { timeout } from 'hono/timeout';
import { swaggerUI } from '@hono/swagger-ui';
import { serveStatic } from 'hono/cloudflare-workers';

import { authMiddleware, refreshTokenAuth, generateTokens } from './middleware/auth';
import { jwtRateLimiter } from './middleware/jwtRateLimiter';
import { rateLimiter } from './middleware/rateLimiter';
import { getLogger } from './utils/logger';

import { securityMiddleware } from './middleware/security';
import { timeoutMiddleware } from './middleware/timeout';
import { loggerMiddleware } from './middleware/logger';
import { DatabaseService } from './db';
import paymentRoutes from './routes/payment';
import dashboardRoutes from './routes/dashboard';
import webhookRoutes from './routes/webhook';
import statusRoutes from './routes/status';
import type { Env } from './types/bindings';

// Extend the globalThis type to include our custom properties
declare global {
  // eslint-disable-next-line no-var
  var app: Hono<{ Bindings: Env }> | undefined;
  // eslint-disable-next-line no-var
  var apiRouter: OpenAPIHono<{ Bindings: Env }> | undefined;
  // eslint-disable-next-line no-var
  var workerApp: Hono<{ Bindings: Env }> | undefined;
}

// Create main application instance
let app: Hono<{ Bindings: Env }>;

// Only create the app once to avoid duplicate handlers
if (!globalThis.app) {
  app = new Hono<{ Bindings: Env }>();
  
  // Apply global middleware
  app.use('*', timeoutMiddleware({ 
    timeout: 30000, // 30 seconds
    message: 'Request processing timeout'
  }));
  
  // Apply CORS middleware
  app.use('*', cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposeHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
    maxAge: 86400,
    credentials: true
  }));
  
  app.use('*', secureHeaders());
  app.use('*', securityMiddleware);
  app.use('*', honoRequestLogger());
  app.use('*', loggerMiddleware);
  
  // Store the app in the global scope
  globalThis.app = app;
} else {
  // Use the existing app instance
  app = globalThis.app as Hono<{ Bindings: Env }>;
}

// Create API router if it doesn't exist
let api: OpenAPIHono<{ Bindings: Env }>;

if (!globalThis.apiRouter) {
  api = new OpenAPIHono<{ Bindings: Env }>();
  
  // Apply rate limiting to API routes
  api.use('*', rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,
    message: 'Too many requests from this IP, please try again later.',
  }));
  
  globalThis.apiRouter = api;
} else {
  api = globalThis.apiRouter as OpenAPIHono<{ Bindings: Env }>;
}

// Initialize database on application startup
api.use('*', async (c, next) => {
  const log = getLogger(c);
  log.info('Initializing database connection');
  
  try {
    const dbService = new DatabaseService(c.env, log, c.env.DB_TYPE as any || 'sqlite');
    await dbService.connect();
    
    log.info('Database connection initialized successfully');
    
    return next();
  } catch (error: any) {
    log.error('Failed to initialize database connection:', error);
    return c.json({ error: 'Database connection failed', details: error.message }, 500);
  }
});

// Apply authentication and rate limiting to API routes
api.use('*', rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests from this IP, please try again later.',
}));

// Apply JWT auth and rate limiting to protected routes
api.use('/payment/*', authMiddleware());
api.use('/status/*', authMiddleware());

// Auth routes
api.post('/auth/refresh', refreshTokenAuth, async (c) => {
  const payload = c.get('jwtPayload');
  const env = c.env;

  // Generate new tokens
  const { accessToken, refreshToken } = await generateTokens(payload, env.JWT_SECRET);

  return c.json({
    access_token: accessToken,
    refresh_token: refreshToken,
    token_type: 'Bearer',
    expires_in: 900 // 15 minutes
  });
});

// Mount API routes
api.route('/payment', paymentRoutes);
api.route('/dashboard', dashboardRoutes);
api.route('/webhooks', webhookRoutes);
api.route('/status', statusRoutes);

// Mount API router to main application
app.route('/api', api);

// Serve static files from public directory
app.use('/*', serveStatic({
  root: './public',
  manifest: ''
}));

// OpenAPI documentation
app.get('/docs', swaggerUI({ url: '/docs/openapi.json' }));

// Root route - will serve index.html from public directory
app.get('/', (c) => c.redirect('/index.html'));

// Error handling
app.onError((err, c) => {
  const log = getLogger(c);
  log.error('Unhandled application error:', err);
  
  return c.json({
    error: 'Internal Server Error',
    message: err.message,
    stack: c.env.ENVIRONMENT === 'development' ? err.stack : undefined
  }, 500);
});

// Ensure we only export the app once
if (!globalThis.workerApp) {
  globalThis.workerApp = app;
}

export default globalThis.workerApp;