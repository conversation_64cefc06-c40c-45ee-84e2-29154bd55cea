import { Context, Next } from 'hono';
import { jwt } from 'hono/jwt';
import { sign } from 'hono/jwt';
import type { Env } from '../types/bindings';
import { AppLogger, getLogger } from '../utils/logger';
import { DatabaseService } from '../db';
import { eq } from 'drizzle-orm';



export interface CustomVariables {
  jwtPayload: {
    iss: string;
    sub: string;
    aud: string;
    iat: number;
    exp: number;
    jti: string;
    type: 'access' | 'refresh';
    apiKey?: string; // Changed from userId to apiKey
    request?: any;
    timestamp: number;
  };
  apiKey?: string;
}

// JWT configuration
export const JWT_CONFIG = {
  issuer: 'newwave-payment-gateway',
  audience: 'payment-api',
  subject: 'api-client',
  accessTokenExpiry: 60 * 15, // 15 minutes
  refreshTokenExpiry: 60 * 60 * 24 * 7 // 7 days
} as const;

// Token blacklist for revoked tokens (in-memory fallback)
const tokenBlacklist = new Set<string>();

// Database-backed token blacklist
export class TokenBlacklistService {
  private dbService: DatabaseService;
  private db: any;
  private log: AppLogger;
  private initialized: boolean = false;
  
  constructor(dbService: DatabaseService, log: AppLogger) {
    this.dbService = dbService;
    this.log = log;
  }
  
  async initialize() {
    if (this.initialized) return;
    
    try {
      this.db = this.dbService.getDb();
      this.initialized = true;
      this.log.info('TokenBlacklistService initialized');
    } catch (error) {
      this.log.error('Failed to initialize TokenBlacklistService:', error);
      throw error;
    }
  }
  
  async addToken(token: string, jti: string, expiresAt: Date) {
    try {
      if (!this.initialized) await this.initialize();
      
      const table = this.dbService.getDbType() === 'postgres' ? 
        this.dbService.getSchema().revokedTokensPg : 
        this.dbService.getSchema().revokedTokensSQLite;
      
      await this.db.insert(table).values({
        id: jti,
        token,
        expiresAt,
        revokedAt: new Date()
      });
      
      this.log.info('Token added to blacklist:', { jti });
      
      // Also add to in-memory blacklist for faster lookups
      tokenBlacklist.add(jti);
    } catch (error) {
      this.log.error('Error adding token to blacklist:', error);
      // Fallback to in-memory blacklist
      tokenBlacklist.add(jti);
    }
  }
  
  async isTokenRevoked(jti: string): Promise<boolean> {
    // First check in-memory blacklist for performance
    if (tokenBlacklist.has(jti)) return true;
    
    try {
      if (!this.initialized) await this.initialize();
      
      const table = this.dbService.getDbType() === 'postgres' ? 
        this.dbService.getSchema().revokedTokensPg : 
        this.dbService.getSchema().revokedTokensSQLite;
      
      const result = await this.db.select()
        .from(table)
        .where(eq(table.id, jti));
      
      if (result.length > 0) {
        // Add to in-memory cache for future lookups
        tokenBlacklist.add(jti);
        return true;
      }
      
      return false;
    } catch (error) {
      this.log.error('Error checking token blacklist:', error);
      // Fallback to in-memory blacklist
      return tokenBlacklist.has(jti);
    }
  }
  
  async cleanupExpiredTokens() {
    try {
      if (!this.initialized) await this.initialize();
      
      const table = this.dbService.getDbType() === 'postgres' ? 
        this.dbService.getSchema().revokedTokensPg : 
        this.dbService.getSchema().revokedTokensSQLite;
      
      const now = new Date();
      await this.db.delete(table)
        .where(table.expiresAt).lt(now);
      
      this.log.info('Expired tokens cleaned up from blacklist');
    } catch (error) {
      this.log.error('Error cleaning up expired tokens:', error);
    }
  }
}

// JWT middleware for access tokens
export const jwtAuth = async (c: Context<{ Variables: CustomVariables; Bindings: Env }>, next: Next) => {
  const log = getLogger(c);

  if (!c.env.JWT_SECRET) {
    log.warn('JWT_SECRET is not set in environment variables');
    return c.json({ error: 'Authentication service unavailable' }, 503);
  }

  log.info('JWT Auth - Starting authentication...');
  const authHeader = c.req.header('Authorization');
  log.debug('JWT Auth - Authorization header:', authHeader);

  const jwtMiddleware = jwt({
    secret: c.env.JWT_SECRET,
    cookie: 'jwt'
  });

  try {
    await jwtMiddleware(c, next);
    const payload = c.get('jwtPayload');
    log.debug('JWT Auth - Token payload:', payload);

    // Verify standard claims
    if (payload.iss !== (c.env.JWT_ISSUER || JWT_CONFIG.issuer)) {
      log.warn('JWT Auth - Invalid issuer:', {
        expected: c.env.JWT_ISSUER || JWT_CONFIG.issuer,
        received: payload.iss
      });
      return c.json({ error: 'Invalid issuer' }, 401);
    }
    if (payload.aud !== (c.env.JWT_AUDIENCE || JWT_CONFIG.audience)) {
      log.warn('JWT Auth - Invalid audience:', {
        expected: c.env.JWT_AUDIENCE || JWT_CONFIG.audience,
        received: payload.aud
      });
      return c.json({ error: 'Invalid audience' }, 401);
    }
    if (payload.type !== 'access') {
      log.warn('JWT Auth - Invalid token type:', {
        expected: 'access',
        received: payload.type
      });
      return c.json({ error: 'Invalid token type' }, 401);
    }
    
    // Check if token is blacklisted
    // First check in-memory blacklist for performance
    if (tokenBlacklist.has(payload.jti)) {
      log.warn('JWT Auth - Token is blacklisted (in-memory):', payload.jti);
      return c.json({ error: 'Token has been revoked' }, 401);
    }
    
    // If database is available, check database blacklist
    try {
      // Initialize database services if needed
      const dbService = new DatabaseService(c.env, log, c.env.DB_TYPE as any || 'sqlite');
      await dbService.connect();
      
      const blacklistService = new TokenBlacklistService(dbService, log);
      await blacklistService.initialize();
      
      const isRevoked = await blacklistService.isTokenRevoked(payload.jti);
      if (isRevoked) {
        log.warn('JWT Auth - Token is blacklisted (database):', payload.jti);
        return c.json({ error: 'Token has been revoked' }, 401);
      }
    } catch (dbError) {
      // If database check fails, log the error but continue with in-memory check only
      log.warn('JWT Auth - Database blacklist check failed:', dbError);
      // We already checked in-memory blacklist, so we can continue
    }

    // Set API key in context if present
    if (payload.apiKey) {
      c.set('apiKey', payload.apiKey);
      // Also store it in the JWT payload for consistency
      payload.apiKey = payload.apiKey;
    }

    log.info('JWT Auth - Authentication successful');
    c.set('jwtPayload', payload);
  } catch (error) {
    log.error('JWT Auth - Authentication failed:', error);
    return c.json({ error: 'Authentication failed' }, 401);
  }
};

// JWT middleware for refresh tokens
export const refreshTokenAuth = async (c: Context<{ Variables: CustomVariables; Bindings: Env }>, next: Next) => {
  const log = getLogger(c);
  
  if (!c.env.JWT_SECRET) {
    log.warn('JWT_SECRET is not set in environment variables');
    return c.json({ error: 'Authentication service unavailable' }, 503);
  }

  const jwtMiddleware = jwt({
    secret: c.env.JWT_SECRET,
    cookie: 'jwt'
  });

  try {
    await jwtMiddleware(c, next);
    const payload = c.get('jwtPayload');

    // Verify standard claims
    if (payload.iss !== JWT_CONFIG.issuer) {
      return c.json({ error: 'Invalid issuer' }, 401);
    }
    if (payload.aud !== JWT_CONFIG.audience) {
      return c.json({ error: 'Invalid audience' }, 401);
    }
    if (payload.type !== 'refresh') {
      return c.json({ error: 'Invalid token type' }, 401);
    }
    // Check if token is blacklisted
    if (tokenBlacklist.has(payload.jti)) {
      return c.json({ error: 'Token has been revoked' }, 401);
    }

    c.set('jwtPayload', payload);
  } catch (error) {
    return c.json({ error: 'Authentication failed' }, 401);
  }
};

// Function to revoke a token
export const revokeToken = async (token: string, env: Env, log: AppLogger) => {
  try {
    const [headerB64, payloadB64] = token.split('.');
    const payload = JSON.parse(atob(payloadB64));
    
    // Add to in-memory blacklist for immediate effect
    tokenBlacklist.add(payload.jti);
    
    // If database is available, add to database blacklist
    try {
      // Initialize database services
      const dbService = new DatabaseService(env, log, env.DB_TYPE as any || 'sqlite');
      await dbService.connect();
      
      const blacklistService = new TokenBlacklistService(dbService, log);
      await blacklistService.initialize();
      
      // Calculate token expiration time
      const expiresAt = new Date(payload.exp * 1000);
      
      // Add token to database blacklist
      await blacklistService.addToken(token, payload.jti, expiresAt);
      
      // Cleanup expired tokens while we're at it
      await blacklistService.cleanupExpiredTokens();
    } catch (dbError) {
      // If database operation fails, log the error but continue with in-memory blacklist
      log.warn('Failed to add token to database blacklist:', dbError);
      // Token is already in in-memory blacklist, so it's still revoked
    }
  } catch (error) {
    throw new Error('Invalid token format');
  }
};

// Helper function to generate both access and refresh tokens
export const generateTokens = async (payload: any, secret: string, env?: Env) => {
  const issuer = env?.JWT_ISSUER || JWT_CONFIG.issuer;
  const audience = env?.JWT_AUDIENCE || JWT_CONFIG.audience;
  const subject = payload.sub || JWT_CONFIG.subject;
  
  const basePayload = {
    iss: issuer,
    sub: subject,
    aud: audience,
    iat: Math.floor(Date.now() / 1000),
    jti: crypto.randomUUID(),
    request: payload.request,
    timestamp: Date.now(),
    apiKey: payload.apiKey // Store API key instead of user ID
  };

  // Generate access token
  const accessTokenPayload = {
    ...basePayload,
    exp: Math.floor(Date.now() / 1000) + JWT_CONFIG.accessTokenExpiry,
    type: 'access' as const
  };

  // Generate refresh token
  const refreshTokenPayload = {
    ...basePayload,
    exp: Math.floor(Date.now() / 1000) + JWT_CONFIG.refreshTokenExpiry,
    type: 'refresh' as const
  };

  // Sign tokens
  const accessToken = await sign(accessTokenPayload, secret);
  const refreshToken = await sign(refreshTokenPayload, secret);

  return { 
    accessToken, 
    refreshToken,
    accessTokenPayload,
    refreshTokenPayload
  };
};

// Authentication middleware for protected routes
export const authMiddleware = () => {
  return async (c: Context<{ Variables: CustomVariables; Bindings: Env }>, next: Next) => {
    const log = getLogger(c);
    log.info('Auth Middleware - Checking authentication');
    
    try {
      await jwtAuth(c, async () => {});
      return next();
    } catch (error: any) {
      log.error('Auth Middleware - Authentication failed:', error);
      return c.json({ 
        error: 'Authentication failed', 
        details: error.message 
      }, 401);
    }
  };
};