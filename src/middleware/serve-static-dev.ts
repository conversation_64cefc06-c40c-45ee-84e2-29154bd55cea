import { Hono } from 'hono';
import * as path from 'path';
import * as fs from 'fs/promises';
import { getLogger } from '../utils/logger';
import type { Env } from '../types/bindings';

export const serveStaticDev = () => {
  return async (c: any, next: any) => {
    const log = getLogger(c);
    log.info('Attempting to serve static file from filesystem...');
    try {
      let filePath = c.req.path === '/' ? '/index.html' : c.req.path;
      const fullPath = path.join(process.cwd(), 'public', filePath);
      log.info(`Serving file from path: ${fullPath}`);

      try {
        await fs.access(fullPath);
        const content = await fs.readFile(fullPath);
        const ext = path.extname(fullPath).toLowerCase();
        const contentTypes: Record<string, string> = {
          '.html': 'text/html', '.css': 'text/css', '.js': 'application/javascript',
          '.json': 'application/json', '.png': 'image/png', '.jpg': 'image/jpeg',
          '.jpeg': 'image/jpeg', '.gif': 'image/gif', '.svg': 'image/svg+xml', '.ico': 'image/x-icon'
        };
        const contentType = contentTypes[ext] || 'application/octet-stream';
        return new Response(content, { headers: { 'Content-Type': contentType } });
      } catch (err) {
        log.warn(`File not found at ${fullPath}. Falling back to SPA index.`);
        if (filePath !== '/index.html') {
          const indexPath = path.join(process.cwd(), 'public', 'index.html');
          try {
            await fs.access(indexPath);
            const content = await fs.readFile(indexPath);
            return new Response(content, { headers: { 'Content-Type': 'text/html' } });
          } catch (indexErr) {
            log.warn('SPA index.html not found. Passing to next middleware.');
            return next();
          }
        }
        return next();
      }
    } catch (error) {
      log.error('Critical error serving static file from filesystem:', error);
      return c.json({ error: 'Internal Server Error' }, 500);
    }
  };
};
