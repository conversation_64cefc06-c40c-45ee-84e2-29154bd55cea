import { v4 as uuidv4 } from 'uuid';
import { PaymentService } from './payment';
import { DatabaseService, TransactionRepository } from '../db';
import { PaymentStatus, PaymentMethod, type NewTransaction, type NewWebhookEvent } from '../db/schema';
import type { Env } from '../types/bindings';
import type { AppLogger } from '../utils/logger';
import type {
  InitiatePaymentPayload,
  DPOInitiatePaymentResponse,
  DPOResponse,
  DPOCheckPaymentStatusResponse
} from '@kazion/dpopay-sdk';

export class TransactionService {
  private paymentService: PaymentService;
  private dbService: DatabaseService;
  private transactionRepo: TransactionRepository;
  private env: Env;
  private log: AppLogger;

  constructor(env: Env, logger: AppLogger) {
    this.env = env;
    this.log = logger;
    this.log.info('Initializing TransactionService');
    this.paymentService = new PaymentService(env, logger);
    this.dbService = new DatabaseService(env, logger, env.DB_TYPE as any || 'sqlite');
    this.transactionRepo = new TransactionRepository(this.dbService.getDb(), this.log, this.env.DB_TYPE as any ||'sqlite');
    this.log.info('TransactionService initialized');
  }

  async initialize() {
    try {
      const db = await this.dbService.connect();
      this.transactionRepo = new TransactionRepository(db, this.log, this.env.DB_TYPE as any || 'sqlite');
      this.log.info('TransactionService initialized successfully');
    } catch (error: any) {
      this.log.error('Failed to initialize TransactionService:', error);
      throw new Error(`TransactionService initialization failed: ${error.message}`);
    }
  }

  async initiatePayment(payload: InitiatePaymentPayload): Promise<DPOInitiatePaymentResponse> {
    try {
      // First, initiate payment with DPO
      const dpoResponse = await this.paymentService.initiatePayment(payload);
      
      // Then, store transaction in database
      const transaction: NewTransaction = {
        id: uuidv4(),
        companyRef: payload.transaction.companyRef,
        transactionToken: dpoResponse.transToken,
        amount: payload.transaction.paymentAmount,
        currency: payload.transaction.paymentCurrency,
        status: PaymentStatus.PENDING,
        customerName: payload.transaction.customerFirstName && payload.transaction.customerLastName 
          ? `${payload.transaction.customerFirstName} ${payload.transaction.customerLastName}` 
          : undefined,
        customerEmail: payload.transaction.customerEmail,
        customerPhone: payload.transaction.customerPhone,
        serviceDescription: payload.services[0]?.serviceDescription,
        metadata: JSON.stringify({
          services: payload.services,
          redirectURL: payload.transaction.redirectURL,
          backURL: payload.transaction.backURL
        }),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await this.transactionRepo.createTransaction(transaction);
      this.log.info('Payment initiated and transaction stored:', { 
        id: transaction.id, 
        token: dpoResponse.transToken,
        amount: transaction.amount,
        currency: transaction.currency
      });

      return dpoResponse;
    } catch (error: any) {
      this.log.error('Error initiating payment transaction:', error);
      throw error;
    }
  }

  async processMobileMoneyPayment(transactionToken: string, phoneNumber: string, network?: string): Promise<DPOResponse> {
    try {
      // First, get transaction from database
      const transaction = await this.transactionRepo.getTransactionByToken(transactionToken);
      if (!transaction) {
        this.log.warn('Transaction not found for mobile money payment:', { transactionToken });
        throw new Error('Transaction not found');
      }

      // Update transaction status to processing
      await this.transactionRepo.updateTransactionStatus(transaction.id, PaymentStatus.PROCESSING);
      
      // Process mobile money payment
      const response = await this.paymentService.processMobileMoney(
        transactionToken,
        phoneNumber,
        network as any
      );

      // Update transaction with payment method
      await this.transactionRepo.updateTransactionStatus(transaction.id, PaymentStatus.COMPLETED);
      
      this.log.info('Mobile money payment processed:', { 
        transactionId: transaction.id,
        transactionToken,
        status: PaymentStatus.COMPLETED
      });

      return response;
    } catch (error: any) {
      this.log.error('Error processing mobile money payment:', error);
      throw error;
    }
  }

  async processCardPayment(
    transactionToken: string,
    cardHolderName: string,
    cardNumber: string,
    expiryMonth: string,
    expiryYear: string,
    cvv: string
  ): Promise<DPOResponse> {
    try {
      // First, get transaction from database
      const transaction = await this.transactionRepo.getTransactionByToken(transactionToken);
      if (!transaction) {
        this.log.warn('Transaction not found for card payment:', { transactionToken });
        throw new Error('Transaction not found');
      }

      // Update transaction status to processing
      await this.transactionRepo.updateTransactionStatus(transaction.id, PaymentStatus.PROCESSING);
      
      // Process card payment
      const response = await this.paymentService.processCard(
        transactionToken,
        cardHolderName,
        cardNumber,
        expiryMonth,
        expiryYear,
        cvv
      );

      // Update transaction with payment method and status
      await this.transactionRepo.updateTransactionStatus(transaction.id, PaymentStatus.COMPLETED);
      
      this.log.info('Card payment processed:', { 
        transactionId: transaction.id,
        transactionToken,
        status: PaymentStatus.COMPLETED
      });

      return response;
    } catch (error: any) {
      this.log.error('Error processing card payment:', error);
      throw error;
    }
  }

  async checkPaymentStatus(transactionToken: string): Promise<DPOCheckPaymentStatusResponse> {
    try {
      // First, get transaction from database
      const transaction = await this.transactionRepo.getTransactionByToken(transactionToken);
      if (!transaction) {
        this.log.warn('Transaction not found for status check:', { transactionToken });
        throw new Error('Transaction not found');
      }

      // Check payment status with DPO
      const response = await this.paymentService.checkPaymentStatus(transactionToken);
      
      // Update transaction status based on DPO response
      if (response.transactionStatus) {
        let newStatus: string;
        
        switch (response.transactionStatus.toUpperCase()) {
          case 'APPROVED':
          case 'SUCCESS':
            newStatus = PaymentStatus.COMPLETED;
            break;
          case 'DECLINED':
          case 'FAILED':
            newStatus = PaymentStatus.FAILED;
            break;
          case 'CANCELLED':
            newStatus = PaymentStatus.CANCELLED;
            break;
          case 'REFUNDED':
            newStatus = PaymentStatus.REFUNDED;
            break;
          case 'PENDING':
          default:
            newStatus = PaymentStatus.PENDING;
        }
        
        await this.transactionRepo.updateTransactionStatus(transaction.id, newStatus as any);
        
        this.log.info('Payment status updated:', { 
          transactionId: transaction.id,
          transactionToken,
          oldStatus: transaction.status,
          newStatus
        });
      }

      return response;
    } catch (error: any) {
      this.log.error('Error checking payment status:', error);
      throw error;
    }
  }

  async cancelPayment(transactionToken: string): Promise<DPOResponse> {
    try {
      // First, get transaction from database
      const transaction = await this.transactionRepo.getTransactionByToken(transactionToken);
      if (!transaction) {
        this.log.warn('Transaction not found for cancellation:', { transactionToken });
        throw new Error('Transaction not found');
      }

      // Cancel payment with DPO
      const response = await this.paymentService.cancelPayment(transactionToken);
      
      // Update transaction status
      await this.transactionRepo.updateTransactionStatus(transaction.id, PaymentStatus.CANCELLED);
      
      this.log.info('Payment cancelled:', { 
        transactionId: transaction.id,
        transactionToken,
        status: PaymentStatus.CANCELLED
      });

      return response;
    } catch (error: any) {
      this.log.error('Error cancelling payment:', error);
      throw error;
    }
  }

  async refundPayment(transactionToken: string, amount: number, reason: string): Promise<DPOResponse> {
    try {
      // First, get transaction from database
      const transaction = await this.transactionRepo.getTransactionByToken(transactionToken);
      if (!transaction) {
        this.log.warn('Transaction not found for refund:', { transactionToken });
        throw new Error('Transaction not found');
      }

      // Refund payment with DPO
      const response = await this.paymentService.refundPayment(transactionToken, amount, reason);
      
      // Create refund record
      await this.transactionRepo.createRefund({
        id: uuidv4(),
        transactionId: transaction.id,
        amount,
        reason,
        status: PaymentStatus.COMPLETED,
        refundedAt: new Date()
      });
      
      // Update transaction status
      const newStatus = amount >= transaction.amount ? 
        PaymentStatus.REFUNDED : 
        PaymentStatus.PARTIALLY_REFUNDED;
      
      await this.transactionRepo.updateTransactionStatus(transaction.id, newStatus);
      
      this.log.info('Payment refunded:', { 
        transactionId: transaction.id,
        transactionToken,
        amount,
        reason,
        status: newStatus
      });

      return response;
    } catch (error: any) {
      this.log.error('Error refunding payment:', error);
      throw error;
    }
  }

  async handleWebhook(xmlData: string): Promise<any> {
    try {
      // Parse webhook data
      const webhookData = this.paymentService.parseWebhookResponse(xmlData);
      const transactionData = webhookData.dpoJsonResponse;
      
      // Log webhook event
      const webhookEvent: NewWebhookEvent = {
        id: uuidv4(),
        eventType: transactionData.statusCode?.toString() || 'UNKNOWN',
        payload: JSON.stringify(webhookData),
        createdAt: new Date(),
      };
      
      // Find transaction by token from the webhook data
      const transactionToken = transactionData.accRef || transactionData.customerCredit;
      
      if (transactionToken) {
        const transaction = await this.transactionRepo.getTransactionByToken(transactionToken);
        if (transaction) {
          webhookEvent.transactionId = transaction.id;
          
          // Update transaction status based on webhook data
          let newStatus: (typeof PaymentStatus)[keyof typeof PaymentStatus];
          switch (transactionData.statusCode?.toString()) {
            case '000': // Success
              newStatus = PaymentStatus.COMPLETED;
              break;
            case '900': // Failed
              newStatus = PaymentStatus.FAILED;
              break;
            case '901': // Cancelled
              newStatus = PaymentStatus.CANCELLED;
              break;
            case '902': // Refunded
              newStatus = PaymentStatus.REFUNDED;
              break;
            case '100': // Pending
            default:
              newStatus = PaymentStatus.PENDING;
          }
          
          await this.transactionRepo.updateTransactionStatus(transaction.id, newStatus);
          
          this.log.info('Transaction status updated from webhook:', { 
            transactionId: transaction.id,
            transactionToken,
            oldStatus: transaction.status,
            newStatus,
            statusMessage: transactionData.message
          });
        } else {
          this.log.warn('Transaction not found for webhook event:', { transactionToken });
        }
      }
      
      // Store webhook event
      await this.transactionRepo.logWebhookEvent(webhookEvent);
      
      this.log.info('Webhook processed successfully:', { 
        webhookEventId: webhookEvent.id,
        eventType: webhookEvent.eventType,
        transactionId: webhookEvent.transactionId
      });
      
      return webhookData;
    } catch (error: any) {
      this.log.error('Error processing webhook:', error);
      throw error;
    }
  }
}