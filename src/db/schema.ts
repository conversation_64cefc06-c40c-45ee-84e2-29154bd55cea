import { sql } from 'drizzle-orm';
import { text, integer, sqliteTable, primaryKey, real, index } from 'drizzle-orm/sqlite-core';
import { pgTable, serial, varchar, timestamp, numeric, uuid, boolean, text as pgText} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// Common payment status enum
export const PaymentStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED'
} as const;

export type PaymentStatusType = typeof PaymentStatus[keyof typeof PaymentStatus];

// Common payment method enum
export const PaymentMethod = {
  MOBILE_MONEY: 'MO<PERSON>LE_MONEY',
  CREDIT_CARD: 'CREDIT_CARD',
  BANK_TRANSFER: 'BANK_TRANSFER',
  OTHER: 'OTHER'
} as const;

export type PaymentMethodType = typeof PaymentMethod[keyof typeof PaymentMethod];

// SQLite Schema
export const transactionsSQLite = sqliteTable('transactions', {
  id: text('id').primaryKey(),
  companyRef: text('company_ref').notNull(),
  transactionToken: text('transaction_token').notNull().unique(),
  amount: real('amount').notNull(),
  currency: text('currency').notNull(),
  status: text('status', { enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED'] }).notNull().default(PaymentStatus.PENDING),
  paymentMethod: text('payment_method', { enum: ['MOBILE_MONEY','CREDIT_CARD', 'BANK_TRANSFER', 'OTHER'] }).notNull().default(PaymentMethod.OTHER),
  customerName: text('customer_name'),
  customerEmail: text('customer_email'),
  customerPhone: text('customer_phone'),
  serviceDescription: text('service_description'),
  metadata: text('metadata', { mode: 'json' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => {
  return {
    statusIdx: index('status_idx').on(table.status),
    companyRefIdx: index('company_ref_idx').on(table.companyRef),
    createdAtIdx: index('created_at_idx').on(table.createdAt),
  };
});

export const refundsSQLite = sqliteTable('refunds', {
  id: text('id').primaryKey(),
  transactionId: text('transaction_id').notNull().references(() => transactionsSQLite.id),
  amount: real('amount').notNull(),
  reason: text('reason'),
  status: text('status', { enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED'] }).notNull().default(PaymentStatus.PENDING),
  refundedAt: integer('refunded_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
});

export const webhookEventsSQLite = sqliteTable('webhook_events', {
  id: text('id').primaryKey(),
  transactionId: text('transaction_id').references(() => transactionsSQLite.id),
  eventType: text('event_type').notNull(),
  payload: text('payload', { mode: 'json' }).notNull(),
  processedAt: integer('processed_at', { mode: 'timestamp' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
});

// API Key schemas
export const apiKeysSQLite = sqliteTable('api_keys', {
  id: text('id').primaryKey(),
  apiKey: text('api_key').notNull().unique(),
  name: text('name').notNull(),
  description: text('description'),
  isActive: integer('is_active', { mode: 'boolean' }).notNull().default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`)
});

export const usersSQLite = sqliteTable('users', {
  id: text('id').primaryKey(),
  username: text('username').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  email: text('email').notNull().unique(),
  role: text('role').notNull().default('user'),
  isActive: integer('is_active', { mode: 'boolean' }).notNull().default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
});

// Revoked tokens table for SQLite
export const revokedTokensSQLite = sqliteTable('revoked_tokens', {
  id: text('id').primaryKey(), // JWT ID (jti)
  token: text('token').notNull(),
  expiresAt: integer('expires_at', { mode: 'timestamp' }).notNull(),
  revokedAt: integer('revoked_at', { mode: 'timestamp' }).notNull().default(sql`CURRENT_TIMESTAMP`),
}, (table) => {
  return {
    expiresAtIdx: index('expires_at_idx').on(table.expiresAt),
  };
});

// PostgreSQL Schema
// API Key schemas for PostgreSQL
export const apiKeysPg = pgTable('api_keys', {
  id: uuid('id').primaryKey().defaultRandom(),
  apiKey: varchar('api_key', { length: 100 }).notNull().unique(),
  name: varchar('name', { length: 100 }).notNull(),
  description: varchar('description', { length: 255 }),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow()
});

// Revoked tokens table for PostgreSQL
export const revokedTokensPg = pgTable('revoked_tokens', {
  id: varchar('id', { length: 100 }).primaryKey(), // JWT ID (jti)
  token: pgText('token').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  revokedAt: timestamp('revoked_at').notNull().defaultNow(),
});

export const transactionsPg = pgTable('transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  companyRef: varchar('company_ref', { length: 100 }).notNull(),
  transactionToken: varchar('transaction_token', { length: 100 }).notNull().unique(),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).notNull(),
  status: pgText('status', { enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED'] }).notNull().default(PaymentStatus.PENDING),
  paymentMethod: varchar('payment_method', { length: 20, enum: ['MOBILE_MONEY','CREDIT_CARD', 'BANK_TRANSFER', 'OTHER'] }).notNull().default(PaymentMethod.OTHER),
  customerName: varchar('customer_name', { length: 100 }),
  customerEmail: varchar('customer_email', { length: 100 }),
  customerPhone: varchar('customer_phone', { length: 20 }),
  serviceDescription: varchar('service_description', { length: 255 }),
  metadata: pgText('metadata'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const refundsPg = pgTable('refunds', {
  id: uuid('id').primaryKey().defaultRandom(),
  transactionId: uuid('transaction_id').notNull().references(() => transactionsPg.id),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  reason: pgText('reason'),
  status: pgText('status', { enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED'] }).notNull().default(PaymentStatus.PENDING),
  refundedAt: timestamp('refunded_at').notNull().defaultNow(),
});

export const webhookEventsPg = pgTable('webhook_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  transactionId: uuid('transaction_id').references(() => transactionsPg.id),
  eventType: varchar('event_type', { length: 50 }).notNull(),
  payload: pgText('payload').notNull(),
  processedAt: timestamp('processed_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const usersPg = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  username: varchar('username', { length: 50 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  email: varchar('email', { length: 100 }).notNull().unique(),
  role: varchar('role', { length: 20 }).notNull().default('user'),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// API Key types
export type ApiKey = typeof apiKeysSQLite.$inferSelect;
export type NewApiKey = typeof apiKeysSQLite.$inferInsert;

// Zod enums
export const transactionStatusEnum = z.enum([
  PaymentStatus.PENDING,
  PaymentStatus.PROCESSING,
  PaymentStatus.COMPLETED,
  PaymentStatus.FAILED,
  PaymentStatus.CANCELLED,
  PaymentStatus.REFUNDED,
  PaymentStatus.PARTIALLY_REFUNDED
]);

export const paymentMethodEnum = z.enum([
  PaymentMethod.MOBILE_MONEY,
  PaymentMethod.CREDIT_CARD,
  PaymentMethod.BANK_TRANSFER,
  PaymentMethod.OTHER
]);

// Helper function to safely parse JSON
const safeJsonParse = (val: unknown): unknown => {
  if (typeof val !== 'string') return val;
  try {
    return JSON.parse(val);
  } catch {
    return val;
  }
};

// Create schemas
export const insertTransactionSchema = createInsertSchema(transactionsSQLite);

export const selectTransactionSchema = createSelectSchema(transactionsSQLite);

export const insertRefundSchema = createInsertSchema(refundsSQLite);

export const selectRefundSchema = createSelectSchema(refundsSQLite);

export const insertWebhookEventSchema = createInsertSchema(webhookEventsSQLite);

export const insertUserSchema = createInsertSchema(usersSQLite);
export const selectUserSchema = createSelectSchema(usersSQLite);

// Types
export type Transaction = typeof transactionsSQLite.$inferSelect & {
  metadata?: Record<string, any>;
};

export type NewTransaction = Omit<typeof transactionsSQLite.$inferInsert, 'metadata'> & {
  metadata?: Record<string, any> | string;
};

export type Refund = typeof refundsSQLite.$inferSelect;
export type NewRefund = typeof refundsSQLite.$inferInsert;

export type WebhookEvent = typeof webhookEventsSQLite.$inferSelect & {
  payload?: Record<string, any>;
};

export type NewWebhookEvent = Omit<typeof webhookEventsSQLite.$inferInsert, 'payload'> & {
  payload: Record<string, any> | string;
};

export type User = typeof usersSQLite.$inferSelect;
export type NewUser = typeof usersSQLite.$inferInsert;