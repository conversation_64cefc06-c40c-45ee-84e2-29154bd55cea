{"name": "newwave-payment-gateway", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wrangler dev", "build": "vite build && vite build --ssr", "preview": "$npm_execpath run build && wrangler dev dist-server/index.js", "deploy": "$npm_execpath run build && wrangler deploy dist-server/index.js", "deploy:production": "$npm_execpath run build && wrangler deploy dist-server/index.js --env production", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "generate-token": "export JWT_SECRET=test_jwt_secret_key_123 && tsx src/scripts/generate-token.ts '{\"transaction\":{\"paymentAmount\":1000,\"paymentCurrency\":\"MWK\",\"companyRef\":\"NW-2024-0507-001\",\"redirectURL\":\"https://dev.newwave.com/payment/success\",\"backURL\":\"https://dev.newwave.com/payment/cancel\",\"customer\":{\"firstName\":\"<PERSON>\",\"lastName\":\"Banda\",\"email\":\"<EMAIL>\",\"phone\":\"+265994567890\"}},\"services\":[{\"serviceType\":\"5525\",\"serviceDescription\":\"Internet Data Bundle\",\"serviceDate\":\"2024-05-07\",\"serviceFrom\":\"LIL\",\"serviceTo\":\"LIL\"}]}'"}, "dependencies": {"@hono/node-server": "^1.14.4", "@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^0.19.8", "@hono/zod-validator": "^0.7.0", "@kazion/dpopay-sdk": "^4.0.17", "@libsql/client": "^0.15.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "bcrypt": "^6.0.0", "better-sqlite3": "^12.0.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "fast-xml-parser": "^5.2.5", "hono": "^4.8.2", "hono-openapi": "^0.4.8", "jsonwebtoken": "^9.0.2", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "serve-static": "^2.2.0", "uuid": "^11.1.0", "zod": "^3.25.67", "zod-openapi": "^4.2.4"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.7.4", "@hono/vite-build": "^1.6.2", "@types/better-sqlite3": "^7.6.13", "@types/node": "^24.0.3", "@types/uuid": "^10.0.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-ssr-hot-reload": "^0.5.0", "wrangler": "^4.20.5"}, "packageManager": "pnpm@10.9.0+sha512.0486e394640d3c1fb3c9d43d49cf92879ff74f8516959c235308f5a8f62e2e19528a65cdc2a3058f587cde71eba3d5b56327c8c33a97e4c4051ca48a10ca2d5f"}