name = "newwave-payment-gateway"
main = "dist-server/index.js"
compatibility_date = "2025-04-03"
compatibility_flags = ["nodejs_compat"]

[build]
command = "pnpm run build"

[env.production.vars]
  ENVIRONMENT = "production"
  ALLOWED_IPS = "https://newwave-payment-gateway.newwavemalawi.workers.dev"
  JWT_ISSUER = "newwave-payment-gateway"
  JWT_AUDIENCE = "payment-api"
  JWT_SUBJECT = "api-client"
  JWT_ACCESS_TOKEN_EXPIRY = "900"
  JWT_REFRESH_TOKEN_EXPIRY = "604800"
  DPO_COMPANY_TOKEN = "8D3DA73D-9D7F-4E09-96D4-3D44E7A83EA3"
  DPO_PAYMENT_URL = "https://secure.3gdirectpay.com/payv3.php"
  DPO_BASE_URL = "https://secure.3gdirectpay.com/API/v6/"
  WEBHOOK_URL = "https://newwave-payment-gateway.newwavemalawi.workers.dev/api/payments/webhook"
  BACK_URL = "https://newwave-payment-gateway.newwavemalawi.workers.dev/payment/back"


[env.development.vars]

  ENVIRONMENT = "development"
  ALLOWED_IPS = "https://newwave-payment-gateway.newwavemalawi.workers.dev"
  SIGNING_SECRET = "2e5b165c7f40817ffa539971e85d4f9769f8278e1ef8510f8f3e16b4ec55804b"
  JWT_SECRET = "30ff9562825dc611938328008b2533b43cd3a3a38ea5ccf11a215cb8cccce5e3"
  JWT_ISSUER = "newwave-payment-gateway"
  JWT_AUDIENCE = "payment-api"
  JWT_SUBJECT = "api-client"
  JWT_ACCESS_TOKEN_EXPIRY = "900"
  JWT_REFRESH_TOKEN_EXPIRY = "604800"
  DPO_COMPANY_TOKEN = "8D3DA73D-9D7F-4E09-96D4-3D44E7A83EA3"
  DPO_PAYMENT_URL = "https://secure.3gdirectpay.com/payv3.php"
  DPO_BASE_URL = "https://secure.3gdirectpay.com/API/v6/"
  WEBHOOK_URL = ""
  BACK_URL = ""
